# p-defer [![Build Status](https://travis-ci.org/sindresorhus/p-defer.svg?branch=master)](https://travis-ci.org/sindresorhus/p-defer)

> Create a deferred promise

[**Don't use this unless you know what you're doing!**](https://github.com/petkaantonov/bluebird/wiki/Promise-anti-patterns#the-deferred-anti-pattern) Prefer the `Promise` constructor.


## Install

```
$ npm install --save p-defer
```


## Usage

```js
const pDefer = require('p-defer');

function delay(ms) {
	const deferred = pDefer();
	setTimeout(deferred.resolve, ms, '🦄');
	return deferred.promise;
}

delay(100).then(console.log);
//=> '🦄'
```

*The above is just an example. Use [`delay`](https://github.com/sindresorhus/delay) if you need to delay a promise.*


## API

### pDefer()

Returns an `Object` with a `promise` property and functions to `resolve()` and `reject()`.


## Related

- [More…](https://github.com/sindresorhus/promise-fun)


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)

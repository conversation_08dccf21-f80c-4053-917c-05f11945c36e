import { Excerpt } from '../mixins/Excerpt';
/**
 * Represents a type referenced via an "extends" or "implements" heritage clause for a TypeScript class.
 * @remarks
 *
 * For example, consider this declaration:
 *
 * ```ts
 * export class Widget extends Controls.WidgetBase implements Controls.IWidget, IDisposable {
 *   // . . .
 * }
 * ```
 *
 * The heritage types are `Controls.WidgetBase`, `Controls.IWidget`, and `IDisposable`.
 * @public
 */
export declare class HeritageType {
    /**
     * An excerpt corresponding to the referenced type.
     * @remarks
     *
     * For example, consider this declaration:
     *
     * ```ts
     * export class Widget extends Controls.WidgetBase implements Controls.IWidget, IDisposable {
     *   // . . .
     * }
     * ```
     *
     * The excerpt might be `Controls.WidgetBase`, `Controls.IWidget`, or `IDisposable`.
     */
    readonly excerpt: Excerpt;
    constructor(excerpt: Excerpt);
}
//# sourceMappingURL=HeritageType.d.ts.map
// Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See <PERSON>IC<PERSON><PERSON> in the project root for license information.


// Communication
@mixin ms-fontColor-communicationShade30 {
  color: $ms-color-communicationShade30;
}
@mixin ms-fontColor-communicationShade20 {
  color: $ms-color-communicationShade20;
}
@mixin ms-fontColor-communicationShade10 {
  color: $ms-color-communicationShade10;
}
@mixin ms-fontColor-communicationPrimary {
  color: $ms-color-communicationPrimary;
}
@mixin ms-fontColor-communicationTint10 {
  color: $ms-color-communicationTint10;
}
@mixin ms-fontColor-communicationTint20 {
  color: $ms-color-communicationTint20;
}
@mixin ms-fontColor-communicationTint30 {
  color: $ms-color-communicationTint30;
}
@mixin ms-fontColor-communicationTint40 {
  color: $ms-color-communicationTint40;
}

// Neutral
@mixin ms-fontColor-black {
  color: $ms-color-black;
}
@mixin ms-fontColor-gray220 {
  color: $ms-color-gray220;
}
@mixin ms-fontColor-gray210 {
  color: $ms-color-gray210;
}
@mixin ms-fontColor-gray200 {
  color: $ms-color-gray200;
}
@mixin ms-fontColor-gray190 {
  color: $ms-color-gray190;
}
@mixin ms-fontColor-gray180 {
  color: $ms-color-gray180;
}
@mixin ms-fontColor-gray170 {
  color: $ms-color-gray170;
}
@mixin ms-fontColor-gray160 {
  color: $ms-color-gray160;
}
@mixin ms-fontColor-gray150 {
  color: $ms-color-gray150;
}
@mixin ms-fontColor-gray140 {
  color: $ms-color-gray140;
}
@mixin ms-fontColor-gray130 {
  color: $ms-color-gray130;
}
@mixin ms-fontColor-gray120 {
  color: $ms-color-gray120;
}
@mixin ms-fontColor-gray110 {
  color: $ms-color-gray110;
}
@mixin ms-fontColor-gray100 {
  color: $ms-color-gray100;
}
@mixin ms-fontColor-gray90 {
  color: $ms-color-gray90;
}
@mixin ms-fontColor-gray80 {
  color: $ms-color-gray80;
}
@mixin ms-fontColor-gray70 {
  color: $ms-color-gray70;
}
@mixin ms-fontColor-gray60 {
  color: $ms-color-gray60;
}
@mixin ms-fontColor-gray50 {
  color: $ms-color-gray50;
}
@mixin ms-fontColor-gray40 {
  color: $ms-color-gray40;
}
@mixin ms-fontColor-gray30 {
  color: $ms-color-gray30;
}
@mixin ms-fontColor-gray20 {
  color: $ms-color-gray20;
}
@mixin ms-fontColor-gray10 {
  color: $ms-color-gray10;
}
@mixin ms-fontColor-white {
  color: $ms-color-white;
}

// Shared
@mixin ms-fontColor-sharedPinkRed10 {
  color: $ms-color-sharedPinkRed10;
}
@mixin ms-fontColor-sharedRed20 {
  color: $ms-color-sharedRed20;
}
@mixin ms-fontColor-sharedRed10 {
  color: $ms-color-sharedRed10;
}
@mixin ms-fontColor-sharedRedOrange20 {
  color: $ms-color-sharedRedOrange20;
}
@mixin ms-fontColor-sharedRedOrange10 {
  color: $ms-color-sharedRedOrange10;
}
@mixin ms-fontColor-sharedOrange30 {
  color: $ms-color-sharedOrange30;
}
@mixin ms-fontColor-sharedOrange20 {
  color: $ms-color-sharedOrange20;
}
@mixin ms-fontColor-sharedOrange10 {
  color: $ms-color-sharedOrange10;
}
@mixin ms-fontColor-sharedYellow10 {
  color: $ms-color-sharedYellow10;
}
@mixin ms-fontColor-sharedOrangeYellow20 {
  color: $ms-color-sharedOrangeYellow20;
}
@mixin ms-fontColor-sharedOrangeYellow10 {
  color: $ms-color-sharedOrangeYellow10;
}
@mixin ms-fontColor-sharedYellowGreen10 {
  color: $ms-color-sharedYellowGreen10;
}
@mixin ms-fontColor-sharedGreen20 {
  color: $ms-color-sharedGreen20;
}
@mixin ms-fontColor-sharedGreen10 {
  color: $ms-color-sharedGreen10;
}
@mixin ms-fontColor-sharedGreenCyan10 {
  color: $ms-color-sharedGreenCyan10;
}
@mixin ms-fontColor-sharedCyan40 {
  color: $ms-color-sharedCyan40;
}
@mixin ms-fontColor-sharedCyan30 {
  color: $ms-color-sharedCyan30;
}
@mixin ms-fontColor-sharedCyan20 {
  color: $ms-color-sharedCyan20;
}
@mixin ms-fontColor-sharedCyan10 {
  color: $ms-color-sharedCyan10;
}
@mixin ms-fontColor-sharedCyanBlue20 {
  color: $ms-color-sharedCyanBlue20;
}
@mixin ms-fontColor-sharedCyanBlue10 {
  color: $ms-color-sharedCyanBlue10;
}
@mixin ms-fontColor-sharedBlue10 {
  color: $ms-color-sharedBlue10;
}
@mixin ms-fontColor-sharedBlueMagenta40 {
  color: $ms-color-sharedBlueMagenta40;
}
@mixin ms-fontColor-sharedBlueMagenta30 {
  color: $ms-color-sharedBlueMagenta30;
}
@mixin ms-fontColor-sharedBlueMagenta20 {
  color: $ms-color-sharedBlueMagenta20;
}
@mixin ms-fontColor-sharedBlueMagenta10 {
  color: $ms-color-sharedBlueMagenta10;
}
@mixin ms-fontColor-sharedMagenta20 {
  color: $ms-color-sharedMagenta20;
}
@mixin ms-fontColor-sharedMagenta10 {
  color: $ms-color-sharedMagenta10;
}
@mixin ms-fontColor-sharedMagentaPink20 {
  color: $ms-color-sharedMagentaPink20;
}
@mixin ms-fontColor-sharedMagentaPink10 {
  color: $ms-color-sharedMagentaPink10;
}
@mixin ms-fontColor-sharedGray40 {
  color: $ms-color-sharedGray40;
}
@mixin ms-fontColor-sharedGray30 {
  color: $ms-color-sharedGray30;
}
@mixin ms-fontColor-sharedGray20 {
  color: $ms-color-sharedGray20;
}
@mixin ms-fontColor-sharedGray10 {
  color: $ms-color-sharedGray10;
}

{"name": "yargs-parser", "version": "5.0.1", "description": "the mighty option parser used by yargs", "main": "index.js", "scripts": {"pretest": "standard", "test": "nyc mocha test/*.js", "coverage": "nyc report --reporter=text-lcov | coveralls", "release": "standard-version"}, "repository": {"url": "**************:yargs/yargs-parser.git"}, "keywords": ["argument", "parser", "yargs", "command", "cli", "parsing", "option", "args", "argument"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.12", "mocha": "^3.0.1", "nyc": "^10.0.0", "standard": "^8.0.0", "standard-version": "^4.0.0"}, "dependencies": {"camelcase": "^3.0.0", "object.assign": "^4.1.0"}, "files": ["lib", "index.js"]}
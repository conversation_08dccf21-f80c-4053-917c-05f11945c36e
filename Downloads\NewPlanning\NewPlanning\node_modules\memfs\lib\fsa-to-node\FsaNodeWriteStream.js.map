{"version": 3, "file": "FsaNodeWriteStream.js", "sourceRoot": "", "sources": ["../../src/fsa-to-node/FsaNodeWriteStream.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAClC,6CAA0C;AAC1C,yDAAsD;AACtD,uCAA6C;AAG7C,sDAA+C;AAK/C;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,MAAa,kBAAmB,SAAQ,iBAAQ;IAO9C,YACE,MAAkC,EAClB,IAAY,EACT,OAA4B;QAE/C,KAAK,EAAE,CAAC;QAHQ,SAAI,GAAJ,IAAI,CAAQ;QACT,YAAO,GAAP,OAAO,CAAqB;QATvC,gBAAW,GAAY,IAAI,CAAC;QAC5B,eAAU,GAAY,KAAK,CAAC;QAC5B,cAAS,GAAW,CAAC,CAAC;QAEb,cAAS,GAAG,IAAA,yBAAW,EAAC,CAAC,CAAC,CAAC;QAQ5C,IAAI,OAAO,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACtC,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;YACzD,CAAC;YACD,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;gBACtB,MAAM,IAAI,SAAS,CAAC,yBAAyB,CAAC,CAAC;YACjD,CAAC;QACH,CAAC;QACD,MAAM,MAAM,GAAG,IAAI,aAAK,EAAiC,CAAC;QAC1D,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC;QACjC,CAAC,KAAK,IAAI,EAAE;;YACV,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC;YAC/B,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YAClC,IAAI,aAAa;gBAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;YACnD,MAAM,KAAK,GAAG,IAAA,oBAAa,EAAC,MAAA,OAAO,CAAC,KAAK,mCAAI,GAAG,CAAC,CAAC;YAClD,MAAM,gBAAgB,GAAG,KAAK,2BAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;YAC9D,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,gBAAgB,EAAE,CAAC,CAAC;YAC3E,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAA,OAAO,CAAC,KAAK,mCAAI,CAAC,CAAC,CAAC;gBACzC,IAAI,KAAK;oBAAE,MAAM,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,CAAC;YACD,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACjB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,OAAiB;QACzC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC9B,IAAI,IAAI,CAAC,UAAU;gBAAE,OAAO;YAC5B,iEAAiE;YACjE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;YACvC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,MAAM,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAC7B,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,UAAU,CAAC;YACtC,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,SAAS;QACrB,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACzC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;YAC9B,IAAI,IAAI,CAAC,UAAU,IAAI,SAAS,EAAE,CAAC;gBACjC,IAAA,wBAAc,EAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;gBACzC,OAAO;YACT,CAAC;YACD,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;gBACvC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACvB,IAAI,SAAS;oBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACpC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;gBAC1B,IAAI,SAAS;oBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,6EAA6E;IAE7E,IAAW,YAAY;QACrB,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAW,OAAO;QAChB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEM,KAAK,CAAC,EAAE;QACb,IAAI,EAAE;YAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC,SAAS,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,GAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IAED,6EAA6E;IAE7E,MAAM,CAAC,KAAU,EAAE,QAAgB,EAAE,QAAwC;QAC3E,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC;aACtB,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,QAAQ;gBAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,QAAQ;gBAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,OAAO,CAAC,MAA+C,EAAE,QAAwC;QAC/F,MAAM,OAAO,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;aACtB,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,QAAQ;gBAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,QAAQ;gBAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;IAED,MAAM,CAAC,QAAwC;QAC7C,IAAI,CAAC,SAAS,EAAE;aACb,IAAI,CAAC,GAAG,EAAE;YACT,IAAI,QAAQ;gBAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/B,CAAC,CAAC;aACD,KAAK,CAAC,KAAK,CAAC,EAAE;YACb,IAAI,QAAQ;gBAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACP,CAAC;CACF;AAvHD,gDAuHC"}
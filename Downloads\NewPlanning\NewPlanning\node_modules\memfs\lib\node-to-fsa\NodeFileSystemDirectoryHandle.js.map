{"version": 3, "file": "NodeFileSystemDirectoryHandle.js", "sourceRoot": "", "sources": ["../../src/node-to-fsa/NodeFileSystemDirectoryHandle.ts"], "names": [], "mappings": ";;;;AAAA,iEAA8D;AAC9D,iCAQgB;AAChB,yEAAsE;AAWtE;;GAEG;AACH,MAAa,6BAA8B,SAAQ,2CAAoB;IAKrE,YACqB,EAAa,EAChC,IAAY,EACZ,MAA+B,EAAE;QAEjC,KAAK,CAAC,WAAW,EAAE,IAAA,eAAQ,EAAC,IAAI,EAAE,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,CAAC,CAAC;QAJtC,OAAE,GAAF,EAAE,CAAW;QAKhC,IAAI,CAAC,GAAG,GAAG,IAAA,UAAS,EAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC;IAChG,CAAC;IAED;;;;;OAKG;IACW,IAAI;;YAChB,MAAM,IAAI,GAAG,sBAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,CAAC;YACzD,KAAK,MAAM,IAAI,IAAI,IAAI;gBAAE,4BAAM,EAAE,GAAG,IAAI,CAAA,CAAC;QAC3C,CAAC;KAAA;IAED;;OAEG;IACW,OAAO;;YACnB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YACvC,MAAM,IAAI,GAAG,sBAAM,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAA,CAAC;YACtE,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE,CAAC;gBACrB,MAAM,MAAM,GAAG,CAAW,CAAC;gBAC3B,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,EAAE,CAAC;gBAC9B,MAAM,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;gBAC5B,IAAI,MAAM,CAAC,WAAW,EAAE;oBAAE,4BAAM,CAAC,IAAI,EAAE,IAAI,6BAA6B,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA,CAAC;qBACvF,IAAI,MAAM,CAAC,MAAM,EAAE;oBAAE,4BAAM,CAAC,IAAI,EAAE,IAAI,mDAAwB,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA,CAAC;YACzF,CAAC;QACH,CAAC;KAAA;IAED;;;;;OAKG;IACW,MAAM;;;;gBAClB,KAA8B,eAAA,KAAA,sBAAA,IAAI,CAAC,OAAO,EAAE,CAAA,IAAA;oBAAd,cAAc;oBAAd,WAAc;oBAAjC,MAAM,CAAC,EAAE,KAAK,CAAC,KAAA,CAAA;oBAAoB,4BAAM,KAAK,CAAA,CAAC;iBAAA;;;;;;;;;QAC5D,CAAC;KAAA;IAED;;;;;;;;;OASG;IACI,KAAK,CAAC,kBAAkB,CAC7B,IAAY,EACZ,OAAmC;QAEnC,IAAA,iBAAU,EAAC,IAAI,EAAE,oBAAoB,EAAE,2BAA2B,CAAC,CAAC;QACpE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;gBAAE,MAAM,IAAA,2BAAoB,GAAE,CAAC;YACvD,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,YAAY;gBAAE,MAAM,KAAK,CAAC;YAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,QAAQ,CAAC,CAAC,CAAC;wBACd,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE,CAAC;4BACpB,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;4BAC/B,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;4BACvC,OAAO,IAAI,6BAA6B,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;wBACxE,CAAC;wBACD,MAAM,IAAA,uBAAgB,GAAE,CAAC;oBAC3B,CAAC;oBACD,KAAK,OAAO,CAAC;oBACb,KAAK,QAAQ;wBACX,MAAM,IAAA,yBAAkB,GAAE,CAAC;gBAC/B,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,aAAa,CAAC,IAAY,EAAE,OAA8B;QACrE,IAAA,iBAAU,EAAC,IAAI,EAAE,eAAe,EAAE,2BAA2B,CAAC,CAAC;QAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACpC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACpD,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;gBAAE,MAAM,IAAA,2BAAoB,GAAE,CAAC;YAClD,OAAO,IAAI,mDAAwB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,YAAY;gBAAE,MAAM,KAAK,CAAC;YAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,QAAQ,CAAC,CAAC,CAAC;wBACd,IAAI,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,EAAE,CAAC;4BACpB,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;4BAC/B,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;4BAC/C,OAAO,IAAI,mDAAwB,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;wBACnE,CAAC;wBACD,MAAM,IAAA,uBAAgB,GAAE,CAAC;oBAC3B,CAAC;oBACD,KAAK,OAAO,CAAC;oBACb,KAAK,QAAQ;wBACX,MAAM,IAAA,yBAAkB,GAAE,CAAC;gBAC/B,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,EAAE,SAAS,GAAG,KAAK,KAAyB,EAAE;QACnF,IAAA,qBAAc,EAAC,IAAI,CAAC,GAAG,CAAC,IAAK,CAAC,CAAC;QAC/B,IAAA,iBAAU,EAAC,IAAI,EAAE,aAAa,EAAE,2BAA2B,CAAC,CAAC;QAC7D,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QACpC,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;QAClC,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC5C,IAAI,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC;gBACnB,MAAM,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAClC,CAAC;iBAAM,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBAC/B,MAAM,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;YAChD,CAAC;;gBAAM,MAAM,IAAA,2BAAoB,GAAE,CAAC;QACtC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,YAAY;gBAAE,MAAM,KAAK,CAAC;YAC/C,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,QAAQ,CAAC,CAAC,CAAC;wBACd,MAAM,IAAA,uBAAgB,GAAE,CAAC;oBAC3B,CAAC;oBACD,KAAK,OAAO,CAAC;oBACb,KAAK,QAAQ;wBACX,MAAM,IAAA,yBAAkB,GAAE,CAAC;oBAC7B,KAAK,WAAW;wBACd,MAAM,IAAI,YAAY,CAAC,6CAA6C,EAAE,0BAA0B,CAAC,CAAC;gBACtG,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,OAAO,CAAC,kBAAwC;QAC3D,IACE,kBAAkB,YAAY,6BAA6B;YAC3D,kBAAkB,YAAY,mDAAwB,EACtD,CAAC;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;YACzB,MAAM,SAAS,GAAG,kBAAkB,CAAC,MAAM,CAAC;YAC5C,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC;YAC7C,IAAI,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC5C,IAAI,QAAQ,KAAK,EAAE;gBAAE,OAAO,EAAE,CAAC;YAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,SAAU,CAAC;YACtC,IAAI,QAAQ,CAAC,CAAC,CAAC,KAAK,SAAS;gBAAE,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5D,OAAO,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACnC,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAjMD,sEAiMC"}
{"version": 3, "file": "FsaCrud.js", "sourceRoot": "", "sources": ["../../src/fsa-to-crud/FsaCrud.ts"], "names": [], "mappings": ";;;;AAEA,8CAAiD;AACjD,uCAA0C;AAC1C,iCAA6F;AAE7F,MAAa,OAAO;IAClB,YACqB,IAA8E;QAA9E,SAAI,GAAJ,IAAI,CAA0E;QAoCnF,QAAG,GAAG,KAAK,EACzB,UAA+B,EAC/B,EAAU,EACV,IAAgB,EAChB,OAA6B,EACd,EAAE;YACjB,IAAA,iBAAU,EAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACxC,IAAA,iBAAU,EAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAChC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAClD,IAAI,IAA2C,CAAC;YAChD,QAAQ,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,OAAO,EAAE,CAAC;gBACzB,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACd,IAAI,CAAC;wBACH,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;wBACtD,MAAM,IAAA,qBAAc,GAAE,CAAC;oBACzB,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe;4BAAE,MAAM,CAAC,CAAC;wBACxC,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;oBACvD,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,KAAK,SAAS,CAAC,CAAC,CAAC;oBACf,IAAI,CAAC;wBACH,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;oBACxD,CAAC;oBAAC,OAAO,CAAC,EAAE,CAAC;wBACX,IAAI,CAAC,CAAC,IAAI,KAAK,eAAe;4BAAE,MAAM,IAAA,sBAAe,GAAE,CAAC;wBACxD,MAAM,CAAC,CAAC;oBACV,CAAC;oBACD,MAAM;gBACR,CAAC;gBACD,OAAO,CAAC,CAAC,CAAC;oBACR,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC;YACD,MAAM,QAAQ,GAAG,MAAM,IAAK,CAAC,cAAc,EAAE,CAAC;YAC9C,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC3B,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC;QAEc,QAAG,GAAG,KAAK,EAAE,UAA+B,EAAE,EAAU,EAAuB,EAAE;YAC/F,IAAA,iBAAU,EAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACxC,IAAA,iBAAU,EAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAChC,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YACpD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YACxC,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;QAChC,CAAC,CAAC;QAEc,QAAG,GAAG,KAAK,EAAE,UAA+B,EAAE,EAAU,EAAE,MAAgB,EAAiB,EAAE;YAC3G,IAAA,iBAAU,EAAC,UAAU,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YACxC,IAAA,iBAAU,EAAC,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAChC,IAAI,CAAC;gBACH,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBACjD,MAAM,GAAG,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM;oBAAE,MAAM,KAAK,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAEc,SAAI,GAAG,KAAK,EAAE,UAA+B,EAAE,EAAW,EAAkC,EAAE;YAC5G,IAAA,iBAAU,EAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACzC,IAAI,EAAE,EAAE,CAAC;gBACP,IAAA,iBAAU,EAAC,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACjC,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;gBACpD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;gBAClC,OAAO;oBACL,IAAI,EAAE,UAAU;oBAChB,EAAE;oBACF,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,QAAQ,EAAE,IAAI,CAAC,YAAY;iBAC5B,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBACrC,OAAO;oBACL,IAAI,EAAE,YAAY;oBAClB,EAAE,EAAE,EAAE;iBACP,CAAC;YACJ,CAAC;QACH,CAAC,CAAC;QAEc,SAAI,GAAG,KAAK,EAAE,UAA+B,EAAE,MAAgB,EAAiB,EAAE;;YAChG,IAAA,iBAAU,EAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC;gBACH,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;gBAC3D,IAAI,MAAM,EAAE,CAAC;oBACX,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC1D,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC;;wBAC7B,KAAyB,eAAA,KAAA,sBAAA,IAAI,CAAC,IAAI,EAAE,CAAA,IAAA;4BAAX,cAAW;4BAAX,WAAW;4BAAzB,MAAM,IAAI,KAAA,CAAA;4BAAiB,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;yBAAA;;;;;;;;;gBAC1F,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM;oBAAE,MAAM,KAAK,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;QAEc,SAAI,GAAG,UACrB,UAA+B;;;gBAE/B,IAAA,iBAAU,EAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;gBACzC,MAAM,CAAC,GAAG,CAAC,GAAG,sBAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAA,CAAC;;oBACnD,KAAiC,eAAA,KAAA,sBAAA,GAAG,CAAC,OAAO,EAAE,CAAA,IAAA,uEAAE,CAAC;wBAAhB,cAAa;wBAAb,WAAa;wBAAnC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,KAAA,CAAA;wBAC3B,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;4BAC3B,4BAAM;gCACJ,IAAI,EAAE,UAAU;gCAChB,EAAE;6BACH,CAAA,CAAC;wBACJ,CAAC;6BAAM,IAAI,MAAM,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;4BACvC,4BAAM;gCACJ,IAAI,EAAE,YAAY;gCAClB,EAAE;6BACH,CAAA,CAAC;wBACJ,CAAC;oBACH,CAAC;;;;;;;;;YACH,CAAC;SAAA,CAAC;QAEc,SAAI,GAAG,KAAK,EAAE,UAA+B,EAAuC,EAAE;;YACpG,MAAM,OAAO,GAA+B,EAAE,CAAC;;gBAC/C,KAA0B,eAAA,KAAA,sBAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA,IAAA;oBAArB,cAAqB;oBAArB,WAAqB;oBAApC,MAAM,KAAK,KAAA,CAAA;oBAA2B,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAAA;;;;;;;;;YACrE,OAAO,OAAO,CAAC;QACjB,CAAC,CAAC;QAEc,SAAI,GAAG,KAAK,EAAE,UAA+B,EAAyB,EAAE;YACtF,IAAA,iBAAU,EAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YACzC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;YAClD,OAAO,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC;QAC1B,CAAC,CAAC;IAhKC,CAAC;IAEM,KAAK,CAAC,MAAM,CACpB,UAA+B,EAC/B,MAAe;QAEf,IAAI,MAAM,GAA+C,SAAS,CAAC;QACnE,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC;QAC1B,IAAI,CAAC;YACH,KAAK,MAAM,IAAI,IAAI,UAAU,EAAE,CAAC;gBAC9B,MAAM,KAAK,GAAG,MAAM,GAAG,CAAC,kBAAkB,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC7D,MAAM,GAAG,GAAG,CAAC;gBACb,GAAG,GAAG,KAAK,CAAC;YACd,CAAC;YACD,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe;gBAAE,MAAM,IAAA,wBAAiB,EAAC,UAAU,CAAC,CAAC;YACxE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAES,KAAK,CAAC,OAAO,CACrB,UAA+B,EAC/B,EAAU;QAEV,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;QACnD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YAC5D,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,eAAe;gBAAE,MAAM,IAAA,sBAAe,EAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC1E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CAgIF;AApKD,0BAoKC"}
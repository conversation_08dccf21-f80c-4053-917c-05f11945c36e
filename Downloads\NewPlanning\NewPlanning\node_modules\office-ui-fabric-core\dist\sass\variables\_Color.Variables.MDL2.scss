// Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See LICENSE in the project root for license information.

// These variables are deprecated and will be removed in a future release.

// Theme
$ms-color-themeDarker: $ms-color-communicationShade30 !default;
$ms-color-themeDark: $ms-color-communicationShade20 !default;
$ms-color-themeDarkAlt: $ms-color-communicationShade10 !default;
$ms-color-themePrimary: $ms-color-communicationPrimary !default;
$ms-color-themeSecondary: $ms-color-communicationTint10 !default;
$ms-color-themeTertiary: #71afe5 !default;
$ms-color-themeLight: $ms-color-communicationTint20 !default;
$ms-color-themeLighter: $ms-color-communicationTint30 !default;
$ms-color-themeLighterAlt: $ms-color-communicationTint40 !default;

// Neutral
// The original colors have been overridden by the new palette, with warmer grays.
$ms-color-black: #000000 !default;
$ms-color-neutralDark: $ms-color-gray190 !default;
$ms-color-neutralPrimary: $ms-color-gray160 !default;
$ms-color-neutralPrimaryAlt: $ms-color-gray150 !default;
$ms-color-neutralSecondary: $ms-color-gray130 !default;
$ms-color-neutralSecondaryAlt: $ms-color-gray120 !default;
$ms-color-neutralTertiary: $ms-color-gray90 !default;
$ms-color-neutralTertiaryAlt: $ms-color-gray60 !default;
$ms-color-neutralQuaternary: $ms-color-gray50 !default;
$ms-color-neutralQuaternaryAlt: $ms-color-gray40 !default;
$ms-color-neutralLight: $ms-color-gray30 !default;
$ms-color-neutralLighter: $ms-color-gray20 !default;
$ms-color-neutralLighterAlt: $ms-color-gray10 !default;
$ms-color-white: #ffffff !default;

// Accent
// Where possible, these colors have been mapped to the new palette.
$ms-color-yellow:        #ffb900 !default;
$ms-color-yellowLight:   #fff100 !default;
$ms-color-orange:        #d83b01 !default;
$ms-color-orangeLight:   #ea4300 !default;
$ms-color-orangeLighter: #ff8c00 !default;
$ms-color-redDark:       #a80000 !default;
$ms-color-red:           #e81123 !default;
$ms-color-magentaDark:   #5c005c !default;
$ms-color-magenta:       #b4009e !default;
$ms-color-magentaLight:  $ms-color-sharedMagentaPink10 !default;
$ms-color-purpleDark:    #32145a !default;
$ms-color-purple:        #5c2d91 !default;
$ms-color-purpleLight:   #b4a0ff !default;
$ms-color-blueDark:      #002050 !default;
$ms-color-blueMid:       #00188f !default;
$ms-color-blue:          #0078d4 !default;
$ms-color-blueLight:     #00bcf2 !default;
$ms-color-tealDark:      #004b50 !default;
$ms-color-teal:          #008272 !default;
$ms-color-tealLight:     #00b294 !default;
$ms-color-greenDark:     #004b1c !default;
$ms-color-green:         #107c10 !default;
$ms-color-greenLight:    #bad80a !default;

// Message
$ms-color-info: $ms-color-neutralSecondaryAlt !default;
$ms-color-infoBackground: $ms-color-neutralLighter !default;
$ms-color-success: $ms-color-green !default;
$ms-color-successBackground: #dff6dd !default;
$ms-color-severeWarning: $ms-color-orange !default;
$ms-color-severeWarningBackground: #fed9cc !default;
$ms-color-alert: $ms-color-severeWarning !default; // Deprecated: Use $ms-color-severeWarning
$ms-color-alertBackground: $ms-color-severeWarningBackground !default; // Deprecated: Use $ms-color-severeWarningBackground
$ms-color-warning: $ms-color-neutralSecondaryAlt !default;
$ms-color-warningBackground: #fff4ce !default;
$ms-color-error: $ms-color-redDark !default;
$ms-color-errorBackground: #fde7e9 !default;

// High contrast
$ms-color-contrastBlackDisabled: #00ff00 !default;
$ms-color-contrastWhiteDisabled: #600000 !default;
$ms-color-contrastBlackSelected: #1aebff !default;
$ms-color-contrastWhiteSelected: #37006e !default;

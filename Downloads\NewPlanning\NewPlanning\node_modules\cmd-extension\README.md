# cmd-extension

> Gets the right command file extension on Windows

[![npm version](https://img.shields.io/npm/v/cmd-extension.svg)](https://www.npmjs.com/package/cmd-extension)

## Installation

```sh
<npm|yarn|pnpm> add cmd-extension
```

## Usage

```js
'use strict'
const CMD_EXTENSION = require('cmd-extension')

console.log(CMD_EXTENSION)
//> .CMD
```

## License

[MIT](./LICENSE) © [<PERSON><PERSON><PERSON>](https://www.kochan.io)

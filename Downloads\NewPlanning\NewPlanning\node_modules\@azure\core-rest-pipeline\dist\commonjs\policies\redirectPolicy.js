"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.redirectPolicyName = void 0;
exports.redirectPolicy = redirectPolicy;
const policies_1 = require("@typespec/ts-http-runtime/internal/policies");
/**
 * The programmatic identifier of the redirectPolicy.
 */
exports.redirectPolicyName = policies_1.redirectPolicyName;
/**
 * A policy to follow Location headers from the server in order
 * to support server-side redirection.
 * In the browser, this policy is not used.
 * @param options - Options to control policy behavior.
 */
function redirectPolicy(options = {}) {
    return (0, policies_1.redirectPolicy)(options);
}
//# sourceMappingURL=redirectPolicy.js.map
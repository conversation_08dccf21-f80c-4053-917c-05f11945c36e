# make-iterator [![NPM version](https://img.shields.io/npm/v/make-iterator.svg?style=flat)](https://www.npmjs.com/package/make-iterator) [![NPM monthly downloads](https://img.shields.io/npm/dm/make-iterator.svg?style=flat)](https://npmjs.org/package/make-iterator) [![NPM total downloads](https://img.shields.io/npm/dt/make-iterator.svg?style=flat)](https://npmjs.org/package/make-iterator) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/make-iterator.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/make-iterator)

> Convert an argument into a valid iterator. Based on the `.makeIterator()` implementation in mout https://github.com/mout/mout.

Please consider following this project's author, [<PERSON>](https://github.com/jonschlinkert), and consider starring the project to show your :heart: and support.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save make-iterator
```

Copyright (c) 2012, 2013 moutjs team and contributors (http://moutjs.com)

## Usage

```js
var iterator = require('make-iterator');
```

**Regex**

```js
var arr = ['a', 'b', 'c', 'd', 'e', 'f'];
var fn = iterator(/[a-c]/);
console.log(arr.filter(fn));
//=> ['a', 'b', 'c'];
```

**Objects**

```js
var fn =  iterator({ a: 1, b: { c: 2 } });

console.log(fn({ a: 1, b: { c: 2, d: 3 } }));
//=> true
console.log(fn({ a: 1, b: { c: 3 } }));
//=> false
```

## About

<details>
<summary><strong>Contributing</strong></summary>

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

</details>

<details>
<summary><strong>Running Tests</strong></summary>

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

</details>

<details>
<summary><strong>Building docs</strong></summary>

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

</details>

### Related projects

You might also be interested in these projects:

* [any](https://www.npmjs.com/package/any): Returns `true` if a value exists in the given string, array or object. | [homepage](https://github.com/jonschlinkert/any "Returns `true` if a value exists in the given string, array or object.")
* [arr-filter](https://www.npmjs.com/package/arr-filter): Faster alternative to javascript's native filter method. | [homepage](https://github.com/jonschlinkert/arr-filter "Faster alternative to javascript's native filter method.")
* [arr-map](https://www.npmjs.com/package/arr-map): Faster, node.js focused alternative to JavaScript's native array map. | [homepage](https://github.com/jonschlinkert/arr-map "Faster, node.js focused alternative to JavaScript's native array map.")
* [array-every](https://www.npmjs.com/package/array-every): Returns true if the callback returns truthy for all elements in the given array. | [homepage](https://github.com/jonschlinkert/array-every "Returns true if the callback returns truthy for all elements in the given array.")
* [collection-map](https://www.npmjs.com/package/collection-map): Returns an array of mapped values from an array or object. | [homepage](https://github.com/jonschlinkert/collection-map "Returns an array of mapped values from an array or object.")
* [utils](https://www.npmjs.com/package/utils): Fast, generic JavaScript/node.js utility functions. | [homepage](https://github.com/jonschlinkert/utils "Fast, generic JavaScript/node.js utility functions.")

### Author

**Jon Schlinkert**

* [LinkedIn Profile](https://linkedin.com/in/jonschlinkert)
* [GitHub Profile](https://github.com/jonschlinkert)
* [Twitter Profile](https://twitter.com/jonschlinkert)

### License

Copyright © 2018, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.6.0, on April 08, 2018._
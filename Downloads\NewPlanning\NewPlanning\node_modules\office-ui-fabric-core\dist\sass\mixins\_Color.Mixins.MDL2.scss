// Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See LICENSE in the project root for license information.

// These mixins are deprecated and will be removed in a future release.

//== Theme colors
//

// Background
@mixin ms-bgColor-themeDark {
  background-color: $ms-color-themeDark;
}
@mixin ms-bgColor-themeDarkAlt {
  background-color: $ms-color-themeDarkAlt;
}
@mixin ms-bgColor-themeDarker {
  background-color: $ms-color-themeDarker;
}
@mixin ms-bgColor-themePrimary {
  background-color: $ms-color-themePrimary;
}
@mixin ms-bgColor-themeSecondary {
  background-color: $ms-color-themeSecondary;
}
@mixin ms-bgColor-themeTertiary {
  background-color: $ms-color-themeTertiary;
}
@mixin ms-bgColor-themeLight {
  background-color: $ms-color-themeLight;
}
@mixin ms-bgColor-themeLighter {
  background-color: $ms-color-themeLighter;
}
@mixin ms-bgColor-themeLighterAlt {
  background-color: $ms-color-themeLighterAlt;
}

// Border
@mixin ms-borderColor-themeDark {
  border-color: $ms-color-themeDark;
}
@mixin ms-borderColor-themeDarkAlt {
  border-color: $ms-color-themeDarkAlt;
}
@mixin ms-borderColor-themeDarker {
  border-color: $ms-color-themeDarker;
}
@mixin ms-borderColor-themePrimary {
  border-color: $ms-color-themePrimary;
}
@mixin ms-borderColor-themeSecondary {
  border-color: $ms-color-themeSecondary;
}
@mixin ms-borderColor-themeTertiary {
  border-color: $ms-color-themeTertiary;
}
@mixin ms-borderColor-themeLight {
  border-color: $ms-color-themeLight;
}
@mixin ms-borderColor-themeLighter {
  border-color: $ms-color-themeLighter;
}
@mixin ms-borderColor-themeLighterAlt {
  border-color: $ms-color-themeLighterAlt;
}

// Font
@mixin ms-fontColor-themeDarker {
  color: $ms-color-themeDarker;
}
@mixin ms-fontColor-themeDark {
  color: $ms-color-themeDark;
}
@mixin ms-fontColor-themeDarkAlt {
  color: $ms-color-themeDarkAlt;
}
@mixin ms-fontColor-themePrimary {
  color: $ms-color-themePrimary;
}
@mixin ms-fontColor-themeSecondary {
  color: $ms-color-themeSecondary;
}
@mixin ms-fontColor-themeTertiary {
  color: $ms-color-themeTertiary;
}
@mixin ms-fontColor-themeLight {
  color: $ms-color-themeLight;
}
@mixin ms-fontColor-themeLighter {
  color: $ms-color-themeLighter;
}
@mixin ms-fontColor-themeLighterAlt {
  color: $ms-color-themeLighterAlt;
}

//== Neutral colors
//

// Background
@mixin ms-bgColor-black {
  background-color: $ms-color-black;
}
@mixin ms-bgColor-neutralDark {
  background-color: $ms-color-neutralDark;
}
@mixin ms-bgColor-neutralPrimary {
  background-color: $ms-color-neutralPrimary;
}
@mixin ms-bgColor-neutralPrimaryAlt {
  background-color: $ms-color-neutralPrimaryAlt;
}
@mixin ms-bgColor-neutralSecondary {
  background-color: $ms-color-neutralSecondary;
}
@mixin ms-bgColor-neutralSecondaryAlt {
  background-color: $ms-color-neutralSecondaryAlt;
}
@mixin ms-bgColor-neutralTertiary {
  background-color: $ms-color-neutralTertiary;
}
@mixin ms-bgColor-neutralTertiaryAlt {
  background-color: $ms-color-neutralTertiaryAlt;
}
@mixin ms-bgColor-neutralQuaternary {
  background-color: $ms-color-neutralQuaternary;
}
@mixin ms-bgColor-neutralQuaternaryAlt {
  background-color: $ms-color-neutralQuaternaryAlt;
}
@mixin ms-bgColor-neutralLight {
  background-color: $ms-color-neutralLight;
}
@mixin ms-bgColor-neutralLighter {
  background-color: $ms-color-neutralLighter;
}
@mixin ms-bgColor-neutralLighterAlt {
  background-color: $ms-color-neutralLighterAlt;
}
@mixin ms-bgColor-white {
  background-color: $ms-color-white;
}

// Border
@mixin ms-borderColor-black {
  border-color: $ms-color-black;
}
@mixin ms-borderColor-neutralDark {
  border-color: $ms-color-neutralDark;
}
@mixin ms-borderColor-neutralPrimary {
  border-color: $ms-color-neutralPrimary;
}
@mixin ms-borderColor-neutralPrimaryAlt {
  border-color: $ms-color-neutralPrimaryAlt;
}
@mixin ms-borderColor-neutralSecondary {
  border-color: $ms-color-neutralSecondary;
}
@mixin ms-borderColor-neutralSecondaryAlt {
  border-color: $ms-color-neutralSecondaryAlt;
}
@mixin ms-borderColor-neutralTertiary {
  border-color: $ms-color-neutralTertiary;
}
@mixin ms-borderColor-neutralTertiaryAlt {
  border-color: $ms-color-neutralTertiaryAlt;
}
@mixin ms-borderColor-neutralQuaternary {
  border-color: $ms-color-neutralQuaternary;
}
@mixin ms-borderColor-neutralQuaternaryAlt {
  border-color: $ms-color-neutralQuaternaryAlt;
}
@mixin ms-borderColor-neutralLight {
  border-color: $ms-color-neutralLight;
}
@mixin ms-borderColor-neutralLighter {
  border-color: $ms-color-neutralLighter;
}
@mixin ms-borderColor-neutralLighterAlt {
  border-color: $ms-color-neutralLighterAlt;
}
@mixin ms-borderColor-white {
  border-color: $ms-color-white;
}

// Font
@mixin ms-fontColor-black {
  color: $ms-color-black;
}
@mixin ms-fontColor-neutralDark {
  color: $ms-color-neutralDark;
}
@mixin ms-fontColor-neutralPrimary {
  color: $ms-color-neutralPrimary;
}
@mixin ms-fontColor-neutralPrimaryAlt {
  color: $ms-color-neutralPrimaryAlt;
}
@mixin ms-fontColor-neutralSecondary {
  color: $ms-color-neutralSecondary;
}
@mixin ms-fontColor-neutralSecondaryAlt {
  color: $ms-color-neutralSecondaryAlt;
}
@mixin ms-fontColor-neutralTertiary {
  color: $ms-color-neutralTertiary;
}
@mixin ms-fontColor-neutralTertiaryAlt {
  color: $ms-color-neutralTertiaryAlt;
}
@mixin ms-fontColor-neutralQuaternary {
  color: $ms-color-neutralQuaternary;
}
@mixin ms-fontColor-neutralQuaternaryAlt {
  color: $ms-color-neutralQuaternaryAlt;
}
@mixin ms-fontColor-neutralLight {
  color: $ms-color-neutralLight;
}
@mixin ms-fontColor-neutralLighter {
  color: $ms-color-neutralLighter;
}
@mixin ms-fontColor-neutralLighterAlt {
  color: $ms-color-neutralLighterAlt;
}
@mixin ms-fontColor-white {
  color: $ms-color-white;
}

//== Brand and accent colors
//

// Background
@mixin ms-bgColor-yellow {
  background-color: $ms-color-yellow;
}
@mixin ms-bgColor-yellowLight {
  background-color: $ms-color-yellowLight;
}
@mixin ms-bgColor-orange {
  background-color: $ms-color-orange;
}
@mixin ms-bgColor-orangeLight {
  background-color: $ms-color-orangeLight;
}
@mixin ms-bgColor-orangeLighter {
  background-color: $ms-color-orangeLighter;
}
@mixin ms-bgColor-redDark {
  background-color: $ms-color-redDark;
}
@mixin ms-bgColor-red {
  background-color: $ms-color-red;
}
@mixin ms-bgColor-magentaDark {
  background-color: $ms-color-magentaDark;
}
@mixin ms-bgColor-magenta {
  background-color: $ms-color-magenta;
}
@mixin ms-bgColor-magentaLight {
  background-color: $ms-color-magentaLight;
}
@mixin ms-bgColor-purpleDark {
  background-color: $ms-color-purpleDark;
}
@mixin ms-bgColor-purple {
  background-color: $ms-color-purple;
}
@mixin ms-bgColor-purpleLight {
  background-color: $ms-color-purpleLight;
}
@mixin ms-bgColor-blueDark {
  background-color: $ms-color-blueDark;
}
@mixin ms-bgColor-blueMid {
  background-color: $ms-color-blueMid;
}
@mixin ms-bgColor-blue {
  background-color: $ms-color-blue;
}
@mixin ms-bgColor-blueLight {
  background-color: $ms-color-blueLight;
}
@mixin ms-bgColor-tealDark {
  background-color: $ms-color-tealDark;
}
@mixin ms-bgColor-teal {
  background-color: $ms-color-teal;
}
@mixin ms-bgColor-tealLight {
  background-color: $ms-color-tealLight;
}
@mixin ms-bgColor-greenDark {
  background-color: $ms-color-greenDark;
}
@mixin ms-bgColor-green {
  background-color: $ms-color-green;
}
@mixin ms-bgColor-greenLight {
  background-color: $ms-color-greenLight;
}

// Border
@mixin ms-borderColor-yellow {
  border-color: $ms-color-yellow;
}
@mixin ms-borderColor-yellowLight {
  border-color: $ms-color-yellowLight;
}
@mixin ms-borderColor-orange {
  border-color: $ms-color-orange;
}
@mixin ms-borderColor-orangeLight {
  border-color: $ms-color-orangeLight;
}
@mixin ms-borderColor-orangeLighter {
  border-color: $ms-color-orangeLighter;
}
@mixin ms-borderColor-redDark {
  border-color: $ms-color-redDark;
}
@mixin ms-borderColor-red {
  border-color: $ms-color-red;
}
@mixin ms-borderColor-magentaDark {
  border-color: $ms-color-magentaDark;
}
@mixin ms-borderColor-magenta {
  border-color: $ms-color-magenta;
}
@mixin ms-borderColor-magentaLight {
  border-color: $ms-color-magentaLight;
}
@mixin ms-borderColor-purpleDark {
  border-color: $ms-color-purpleDark;
}
@mixin ms-borderColor-purple {
  border-color: $ms-color-purple;
}
@mixin ms-borderColor-purpleLight {
  border-color: $ms-color-purpleLight;
}
@mixin ms-borderColor-blueDark {
  border-color: $ms-color-blueDark;
}
@mixin ms-borderColor-blueMid {
  border-color: $ms-color-blueMid;
}
@mixin ms-borderColor-blue {
  border-color: $ms-color-blue;
}
@mixin ms-borderColor-blueLight {
  border-color: $ms-color-blueLight;
}
@mixin ms-borderColor-tealDark {
  border-color: $ms-color-tealDark;
}
@mixin ms-borderColor-teal {
  border-color: $ms-color-teal;
}
@mixin ms-borderColor-tealLight {
  border-color: $ms-color-tealLight;
}
@mixin ms-borderColor-greenDark {
  border-color: $ms-color-greenDark;
}
@mixin ms-borderColor-green {
  border-color: $ms-color-green;
}
@mixin ms-borderColor-greenLight {
  border-color: $ms-color-greenLight;
}

// Font
@mixin ms-fontColor-yellow {
  color: $ms-color-yellow;
}
@mixin ms-fontColor-yellowLight {
  color: $ms-color-yellowLight;
}
@mixin ms-fontColor-orange {
  color: $ms-color-orange;
}
@mixin ms-fontColor-orangeLight {
  color: $ms-color-orangeLight;
}
@mixin ms-fontColor-orangeLighter {
  color: $ms-color-orangeLighter;
}
@mixin ms-fontColor-redDark {
  color: $ms-color-redDark;
}
@mixin ms-fontColor-red {
  color: $ms-color-red;
}
@mixin ms-fontColor-magentaDark {
  color: $ms-color-magentaDark;
}
@mixin ms-fontColor-magenta {
  color: $ms-color-magenta;
}
@mixin ms-fontColor-magentaLight {
  color: $ms-color-magentaLight;
}
@mixin ms-fontColor-purpleDark {
  color: $ms-color-purpleDark;
}
@mixin ms-fontColor-purple {
  color: $ms-color-purple;
}
@mixin ms-fontColor-purpleLight {
  color: $ms-color-purpleLight;
}
@mixin ms-fontColor-blueDark {
  color: $ms-color-blueDark;
}
@mixin ms-fontColor-blueMid {
  color: $ms-color-blueMid;
}
@mixin ms-fontColor-blue {
  color: $ms-color-blue;
}
@mixin ms-fontColor-blueLight {
  color: $ms-color-blueLight;
}
@mixin ms-fontColor-tealDark {
  color: $ms-color-tealDark;
}
@mixin ms-fontColor-teal {
  color: $ms-color-teal;
}
@mixin ms-fontColor-tealLight {
  color: $ms-color-tealLight;
}
@mixin ms-fontColor-greenDark {
  color: $ms-color-greenDark;
}
@mixin ms-fontColor-green {
  color: $ms-color-green;
}
@mixin ms-fontColor-greenLight {
  color: $ms-color-greenLight;
}

//== Message colors
//

// Background
@mixin ms-bgColor-info {
  background-color: $ms-color-infoBackground;
}
@mixin ms-bgColor-success {
  background-color: $ms-color-successBackground;
}
@mixin ms-bgColor-severeWarning {
  background-color: $ms-color-severeWarningBackground;
}
@mixin ms-bgColor-warning {
  background-color: $ms-color-warningBackground;
}
@mixin ms-bgColor-error {
  background-color: $ms-color-errorBackground;
}

// Border
@mixin ms-borderColor-info {
  border-color: $ms-color-info;
}
@mixin ms-borderColor-success {
  border-color: $ms-color-success;
}
@mixin ms-borderColor-alert {
  border-color: $ms-color-alert;
}
@mixin ms-borderColor-error {
  border-color: $ms-color-error;
}

// Font
@mixin ms-fontColor-info {
  color: $ms-color-info;
}
@mixin ms-fontColor-success {
  color: $ms-color-success;
}
@mixin ms-fontColor-alert {
  // @todo: Deprecated: Use ms-fontColor-severeWarning
  color: $ms-color-alert;
}
@mixin ms-fontColor-warning {
  color: $ms-color-warning;
}
@mixin ms-fontColor-severeWarning {
  color: $ms-color-severeWarning;
}
@mixin ms-fontColor-error {
  color: $ms-color-error;
}


//== High contrast colors
//

// Background
@mixin ms-bgColor-contrastBlackDisabled {
  background-color: $ms-color-contrastBlackDisabled;
}
@mixin ms-bgColor-contrastWhiteDisabled {
  background-color: $ms-color-contrastWhiteDisabled;
}
@mixin ms-bgColor-contrastBlackSelected {
  background-color: $ms-color-contrastBlackSelected;
}
@mixin ms-bgColor-contrastWhiteSelected {
  background-color: $ms-color-contrastWhiteSelected;
}

// Border
@mixin ms-borderColor-contrastBlackDisabled {
  border-color: $ms-color-contrastBlackDisabled;
}
@mixin ms-borderColor-contrastWhiteDisabled {
  border-color: $ms-color-contrastWhiteDisabled;
}
@mixin ms-borderColor-contrastBlackSelected {
  border-color: $ms-color-contrastBlackSelected;
}
@mixin ms-borderColor-contrastWhiteSelected {
  border-color: $ms-color-contrastWhiteSelected;
}

// Font
@mixin ms-fontColor-contrastBlackDisabled {
  color: $ms-color-contrastBlackDisabled;
}
@mixin ms-fontColor-contrastWhiteDisabled {
  color: $ms-color-contrastWhiteDisabled;
}
@mixin ms-fontColor-contrastBlackSelected {
  color: $ms-color-contrastBlackSelected;
}
@mixin ms-fontColor-contrastWhiteSelected {
  color: $ms-color-contrastWhiteSelected;
}

'use strict';

/**
 * Parse the content of a passwd file into a list of user objects.
 * This function ignores blank lines and comments.
 *
 * ```js
 * // assuming '/etc/passwd' contains:
 * // doowb:*:123:123:<PERSON>:/Users/<USER>/bin/bash
 * console.log(parse(fs.readFileSync('/etc/passwd', 'utf8')));
 *
 * //=> [
 * //=>   {
 * //=>     username: 'doowb',
 * //=>     password: '*',
 * //=>     uid: '123',
 * //=>     gid: '123',
 * //=>     gecos: '<PERSON>',
 * //=>     homedir: '/Users/<USER>',
 * //=>     shell: '/bin/bash'
 * //=>   }
 * //=> ]
 * ```
 * @param  {String} `content` Content of a passwd file to parse.
 * @return {Array} Array of user objects parsed from the content.
 * @api public
 */

module.exports = function(content) {
  if (typeof content !== 'string') {
    throw new Error('expected a string');
  }
  return content
    .split('\n')
    .map(user)
    .filter(Boolean);
};

function user(line, i) {
  if (!line || !line.length || line.charAt(0) === '#') {
    return null;
  }

  // see https://en.wikipedia.org/wiki/Passwd for field descriptions
  var fields = line.split(':');
  return {
    username: fields[0],
    password: fields[1],
    uid: fields[2],
    gid: fields[3],
    // see https://en.wikipedia.org/wiki/Gecos_field for GECOS field descriptions
    gecos: fields[4],
    homedir: fields[5],
    shell: fields[6]
  };
}

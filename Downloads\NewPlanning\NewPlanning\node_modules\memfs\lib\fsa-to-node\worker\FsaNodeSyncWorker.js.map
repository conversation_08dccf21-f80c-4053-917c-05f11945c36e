{"version": 3, "file": "FsaNodeSyncWorker.js", "sourceRoot": "", "sources": ["../../../src/fsa-to-node/worker/FsaNodeSyncWorker.ts"], "names": [], "mappings": ";;;AAAA,mDAA+D;AAE/D,4CAAyC;AACzC,kCAA2C;AAY3C,MAAa,iBAAiB;IAA9B;QACqB,QAAG,GAAsB,IAAI,iBAAiB,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAC5D,cAAS,GAAG,IAAI,6BAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAajD,kBAAa,GAAG,CAAC,GAAqB,EAAQ,EAAE;YACxD,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBACf,6CAAqC,CAAC,CAAC,CAAC;oBACtC,MAAM,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;oBACxB,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;oBAChB,IAAI,CAAC,EAAE,GAAG,IAAI,qBAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACnC,MAAM,QAAQ,GAA4B,2CAAmC,EAAE,CAAC,CAAC;oBACjF,WAAW,CAAC,QAAQ,CAAC,CAAC;oBACtB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC1C,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QAEiB,cAAS,GAAkB,KAAK,EAAE,OAAmB,EAAuB,EAAE;YAC/F,IAAI,CAAC;gBACH,MAAM,OAAO,GAAG,cAAO,CAAC,MAAM,CAAC,OAAc,CAA4B,CAAC;gBAC1E,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;oBAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;gBACvE,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACxB,IAAI,IAAI,6CAAqC;oBAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;gBACvF,MAAM,CAAC,EAAE,MAAM,EAAE,OAAO,CAAC,GAAG,OAAO,CAAC;gBACpC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACtC,IAAI,CAAC,OAAO;oBAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,MAAM,EAAE,CAAC,CAAC;gBAC1D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,CAAC;gBACxC,OAAO,cAAO,CAAC,MAAM,CAAC,4CAAoC,QAAQ,CAAC,CAAC,CAAC;YACvE,CAAC;YAAC,OAAO,GAAG,EAAE,CAAC;gBACb,MAAM,OAAO,GAAG,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;gBAC9F,MAAM,KAAK,GAAuB,EAAE,OAAO,EAAE,CAAC;gBAC9C,IAAI,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;oBAAE,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;gBAChG,OAAO,cAAO,CAAC,MAAM,CAAC,iDAAyC,KAAK,CAAC,CAAC,CAAC;YACzE,CAAC;QACH,CAAC,CAAC;QAmDQ,aAAQ,GAId;YACF,IAAI,EAAE,KAAK,EAAE,QAAoB,EAAoC,EAAE;gBACrE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;gBAC7E,OAAO;oBACL,IAAI,EAAE,MAAM,CAAC,IAAI;iBAClB,CAAC;YACJ,CAAC;YACD,MAAM,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAiB,EAAE;gBAChD,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YAChD,CAAC;YACD,QAAQ,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAuB,EAAE;gBACxD,MAAM,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,kCAAO,IAAI,KAAE,QAAQ,EAAE,QAAQ,IAAG,CAAW,CAAC;gBACnG,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,GAAG,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;gBAClE,OAAO,KAAK,CAAC;YACf,CAAC;YACD,SAAS,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,EAAiB,EAAE;gBACzD,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,kCAAO,IAAI,KAAE,QAAQ,EAAE,QAAQ,IAAG,CAAC;YACpF,CAAC;YACD,UAAU,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,EAAiB,EAAE;gBAC1D,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,IAAI,kCAAO,IAAI,KAAE,QAAQ,EAAE,QAAQ,IAAG,CAAC;YACrF,CAAC;YACD,IAAI,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,EAAiB,EAAE;gBAC/C,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACnD,CAAC;YACD,IAAI,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAiB,EAAE;gBACxC,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;YAC1C,CAAC;YACD,KAAK,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAiB,EAAE;gBAClD,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAClD,CAAC;YACD,EAAE,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAiB,EAAE;gBAC/C,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC/C,CAAC;YACD,KAAK,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,EAA+B,EAAE;gBAChE,OAAO,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YACzD,CAAC;YACD,OAAO,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAmB,EAAE;gBAC7C,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAW,CAAC;YACpF,CAAC;YACD,KAAK,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAiB,EAAE;gBAC9C,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;YACjD,CAAC;YACD,MAAM,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAiB,EAAE;gBAC1C,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YAC1C,CAAC;YACD,OAAO,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,EAAsC,EAAE;gBAChE,MAAM,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC,CAAc,CAAC;gBAChH,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAClB,KAAK,CAAC,EAAE,CACN,CAAyB;oBACvB,IAAI,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;oBAChD,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB,CAAA,CACJ,CAAC;gBACF,OAAO,GAAG,CAAC;YACb,CAAC;YACD,IAAI,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAuB,EAAE;gBAChE,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;gBACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC1D,MAAM,SAAS,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;oBAC9D,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,EAAE;wBACrE,IAAI,GAAG;4BAAE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;wBAC5B,OAAO,CAAC,SAAS,IAAI,MAAM,CAAC,CAAC;oBAC/B,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,IAAI,SAAS,GAAG,MAAM;oBAAE,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;gBAC1D,OAAO,KAAK,CAAC;YACf,CAAC;YACD,KAAK,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC,EAAmB,EAAE;gBAC3D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC1D,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ,IAAI,SAAS,CAAC,CAAC;gBACzF,OAAO,YAAY,CAAC;YACtB,CAAC;YACD,IAAI,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,EAAsC,EAAE;gBAC1E,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBAClE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBACpD,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC;YACd,CAAC;SACF,CAAC;IACJ,CAAC;IA/KQ,KAAK;QACV,SAAS,GAAG,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;gBAAE,OAAO;YACnC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAwB,CAAC,CAAC;QACjD,CAAC,CAAC;QACF,MAAM,OAAO,GAAyB,wCAAgC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChF,WAAW,CAAC,OAAO,CAAC,CAAC;IACvB,CAAC;IAmCS,KAAK,CAAC,MAAM,CAAC,IAAc,EAAE,MAAe,EAAE,QAAiB;QACvE,IAAI,IAAI,GAAmC,IAAI,CAAC,IAAI,CAAC;QACrD,MAAM,OAAO,GAAkC,EAAE,MAAM,EAAE,CAAC;QAC1D,IAAI,CAAC;YACH,KAAK,MAAM,IAAI,IAAI,IAAI,EAAE,CAAC;gBACxB,IAAI,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACtD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,gFAAgF;YAChF,qFAAqF;YACrF,MAAM,KAAK,CAAC;QACd,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAES,KAAK,CAAC,OAAO,CACrB,IAAc,EACd,IAAY,EACZ,QAAiB,EACjB,MAAgB;QAEhB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACrD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC;IACd,CAAC;IAES,KAAK,CAAC,YAAY,CAC1B,IAAc,EACd,IAAY,EACZ,QAAiB,EACjB,MAAgB;QAEhB,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACvC,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;oBACnB,KAAK,mBAAmB;wBACtB,OAAO,MAAM,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;oBAC5C,wBAAwB;oBACxB,oFAAoF;gBACtF,CAAC;YACH,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CAsFF;AArLD,8CAqLC"}
// Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See LICENSE in the project root for license information.

// Communication
.ms-borderColor-communicationShade30,
.ms-borderColor-communicationShade30--hover:hover {
  @include ms-borderColor-communicationShade30;
}
.ms-borderColor-communicationShade20,
.ms-borderColor-communicationShade20--hover:hover {
  @include ms-borderColor-communicationShade20;
}
.ms-borderColor-communicationShade10,
.ms-borderColor-communicationShade10--hover:hover {
  @include ms-borderColor-communicationShade10;
}
.ms-borderColor-communicationPrimary,
.ms-borderColor-communicationPrimary--hover:hover {
  @include ms-borderColor-communicationPrimary;
}
.ms-borderColor-communicationTint10,
.ms-borderColor-communicationTint10--hover:hover {
  @include ms-borderColor-communicationTint10;
}
.ms-borderColor-communicationTint20,
.ms-borderColor-communicationTint20--hover:hover {
  @include ms-borderColor-communicationTint20;
}
.ms-borderColor-communicationTint30,
.ms-borderColor-communicationTint30--hover:hover {
  @include ms-borderColor-communicationTint30;
}
.ms-borderColor-communicationTint40,
.ms-borderColor-communicationTint40--hover:hover {
  @include ms-borderColor-communicationTint40;
}

// Neutral
.ms-borderColor-black,
.ms-borderColor-black--hover:hover {
  @include ms-borderColor-black;
}
.ms-borderColor-gray220,
.ms-borderColor-gray220--hover:hover {
  @include ms-borderColor-gray220;
}
.ms-borderColor-gray210,
.ms-borderColor-gray210--hover:hover {
  @include ms-borderColor-gray210;
}
.ms-borderColor-gray200,
.ms-borderColor-gray200--hover:hover {
  @include ms-borderColor-gray200;
}
.ms-borderColor-gray190,
.ms-borderColor-gray190--hover:hover {
  @include ms-borderColor-gray190;
}
.ms-borderColor-gray180,
.ms-borderColor-gray180--hover:hover {
  @include ms-borderColor-gray180;
}
.ms-borderColor-gray170,
.ms-borderColor-gray170--hover:hover {
  @include ms-borderColor-gray170;
}
.ms-borderColor-gray160,
.ms-borderColor-gray160--hover:hover {
  @include ms-borderColor-gray160;
}
.ms-borderColor-gray150,
.ms-borderColor-gray150--hover:hover {
  @include ms-borderColor-gray150;
}
.ms-borderColor-gray140,
.ms-borderColor-gray140--hover:hover {
  @include ms-borderColor-gray140;
}
.ms-borderColor-gray130,
.ms-borderColor-gray130--hover:hover {
  @include ms-borderColor-gray130;
}
.ms-borderColor-gray120,
.ms-borderColor-gray120--hover:hover {
  @include ms-borderColor-gray120;
}
.ms-borderColor-gray110,
.ms-borderColor-gray110--hover:hover {
  @include ms-borderColor-gray110;
}
.ms-borderColor-gray100,
.ms-borderColor-gray100--hover:hover {
  @include ms-borderColor-gray100;
}
.ms-borderColor-gray90,
.ms-borderColor-gray90--hover:hover {
  @include ms-borderColor-gray90;
}
.ms-borderColor-gray80,
.ms-borderColor-gray80--hover:hover {
  @include ms-borderColor-gray80;
}
.ms-borderColor-gray70,
.ms-borderColor-gray70--hover:hover {
  @include ms-borderColor-gray70;
}
.ms-borderColor-gray60,
.ms-borderColor-gray60--hover:hover {
  @include ms-borderColor-gray60;
}
.ms-borderColor-gray50,
.ms-borderColor-gray50--hover:hover {
  @include ms-borderColor-gray50;
}
.ms-borderColor-gray40,
.ms-borderColor-gray40--hover:hover {
  @include ms-borderColor-gray40;
}
.ms-borderColor-gray30,
.ms-borderColor-gray30--hover:hover {
  @include ms-borderColor-gray30;
}
.ms-borderColor-gray20,
.ms-borderColor-gray20--hover:hover {
  @include ms-borderColor-gray20;
}
.ms-borderColor-gray10,
.ms-borderColor-gray10--hover:hover {
  @include ms-borderColor-gray10;
}
.ms-borderColor-white,
.ms-borderColor-white--hover:hover {
  @include ms-borderColor-white;
}

// Shared
.ms-borderColor-sharedPinkRed10,
.ms-borderColor-sharedPinkRed10--hover:hover {
  @include ms-borderColor-sharedPinkRed10;
}
.ms-borderColor-sharedRed20,
.ms-borderColor-sharedRed20--hover:hover {
  @include ms-borderColor-sharedRed20;
}
.ms-borderColor-sharedRed10,
.ms-borderColor-sharedRed10--hover:hover {
  @include ms-borderColor-sharedRed10;
}
.ms-borderColor-sharedRedOrange20,
.ms-borderColor-sharedRedOrange20--hover:hover {
  @include ms-borderColor-sharedRedOrange20;
}
.ms-borderColor-sharedRedOrange10,
.ms-borderColor-sharedRedOrange10--hover:hover {
  @include ms-borderColor-sharedRedOrange10;
}
.ms-borderColor-sharedOrange30,
.ms-borderColor-sharedOrange30--hover:hover {
  @include ms-borderColor-sharedOrange30;
}
.ms-borderColor-sharedOrange20,
.ms-borderColor-sharedOrange20--hover:hover {
  @include ms-borderColor-sharedOrange20;
}
.ms-borderColor-sharedOrange10,
.ms-borderColor-sharedOrange10--hover:hover {
  @include ms-borderColor-sharedOrange10;
}
.ms-borderColor-sharedYellow10,
.ms-borderColor-sharedYellow10--hover:hover {
  @include ms-borderColor-sharedYellow10;
}
.ms-borderColor-sharedOrangeYellow20,
.ms-borderColor-sharedOrangeYellow20--hover:hover {
  @include ms-borderColor-sharedOrangeYellow20;
}
.ms-borderColor-sharedOrangeYellow10,
.ms-borderColor-sharedOrangeYellow10--hover:hover {
  @include ms-borderColor-sharedOrangeYellow10;
}
.ms-borderColor-sharedYellowGreen10,
.ms-borderColor-sharedYellowGreen10--hover:hover {
  @include ms-borderColor-sharedYellowGreen10;
}
.ms-borderColor-sharedGreen20,
.ms-borderColor-sharedGreen20--hover:hover {
  @include ms-borderColor-sharedGreen20;
}
.ms-borderColor-sharedGreen10,
.ms-borderColor-sharedGreen10--hover:hover {
  @include ms-borderColor-sharedGreen10;
}
.ms-borderColor-sharedGreenCyan10,
.ms-borderColor-sharedGreenCyan10--hover:hover {
  @include ms-borderColor-sharedGreenCyan10;
}
.ms-borderColor-sharedCyan40,
.ms-borderColor-sharedCyan40--hover:hover {
  @include ms-borderColor-sharedCyan40;
}
.ms-borderColor-sharedCyan30,
.ms-borderColor-sharedCyan30--hover:hover {
  @include ms-borderColor-sharedCyan30;
}
.ms-borderColor-sharedCyan20,
.ms-borderColor-sharedCyan20--hover:hover {
  @include ms-borderColor-sharedCyan20;
}
.ms-borderColor-sharedCyan10,
.ms-borderColor-sharedCyan10--hover:hover {
  @include ms-borderColor-sharedCyan10;
}
.ms-borderColor-sharedCyanBlue20,
.ms-borderColor-sharedCyanBlue20--hover:hover {
  @include ms-borderColor-sharedCyanBlue20;
}
.ms-borderColor-sharedCyanBlue10,
.ms-borderColor-sharedCyanBlue10--hover:hover {
  @include ms-borderColor-sharedCyanBlue10;
}
.ms-borderColor-sharedBlue10,
.ms-borderColor-sharedBlue10--hover:hover {
  @include ms-borderColor-sharedBlue10;
}
.ms-borderColor-sharedBlueMagenta40,
.ms-borderColor-sharedBlueMagenta40--hover:hover {
  @include ms-borderColor-sharedBlueMagenta40;
}
.ms-borderColor-sharedBlueMagenta30,
.ms-borderColor-sharedBlueMagenta30--hover:hover {
  @include ms-borderColor-sharedBlueMagenta30;
}
.ms-borderColor-sharedBlueMagenta20,
.ms-borderColor-sharedBlueMagenta20--hover:hover {
  @include ms-borderColor-sharedBlueMagenta20;
}
.ms-borderColor-sharedBlueMagenta10,
.ms-borderColor-sharedBlueMagenta10--hover:hover {
  @include ms-borderColor-sharedBlueMagenta10;
}
.ms-borderColor-sharedMagenta20,
.ms-borderColor-sharedMagenta20--hover:hover {
  @include ms-borderColor-sharedMagenta20;
}
.ms-borderColor-sharedMagenta10,
.ms-borderColor-sharedMagenta10--hover:hover {
  @include ms-borderColor-sharedMagenta10;
}
.ms-borderColor-sharedMagentaPink20,
.ms-borderColor-sharedMagentaPink20--hover:hover {
  @include ms-borderColor-sharedMagentaPink20;
}
.ms-borderColor-sharedMagentaPink10,
.ms-borderColor-sharedMagentaPink10--hover:hover {
  @include ms-borderColor-sharedMagentaPink10;
}
.ms-borderColor-sharedGray40,
.ms-borderColor-sharedGray40--hover:hover {
  @include ms-borderColor-sharedGray40;
}
.ms-borderColor-sharedGray30,
.ms-borderColor-sharedGray30--hover:hover {
  @include ms-borderColor-sharedGray30;
}
.ms-borderColor-sharedGray20,
.ms-borderColor-sharedGray20--hover:hover {
  @include ms-borderColor-sharedGray20;
}
.ms-borderColor-sharedGray10,
.ms-borderColor-sharedGray10--hover:hover {
  @include ms-borderColor-sharedGray10;
}

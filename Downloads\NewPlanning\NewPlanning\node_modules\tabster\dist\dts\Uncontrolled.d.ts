/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import * as Types from "./Types";
/**
 * Allows default or user focus behaviour on the DOM subtree
 * i.e. <PERSON><PERSON><PERSON> will not control focus events within an uncontrolled area
 */
export declare class UncontrolledAPI implements Types.UncontrolledAPI {
    private _isUncontrolledCompletely?;
    constructor(isUncontrolledCompletely?: (element: HTMLElement, completely: boolean) => boolean | undefined);
    isUncontrolledCompletely(element: HTMLElement, completely: boolean): boolean;
}

// Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See LICENSE in the project root for license information.

// These classes are deprecated and will be removed in a future release.

//== Theme colors
//

// Background
.ms-bgColor-themeDark,
.ms-bgColor-themeDark--hover:hover {
  @include ms-bgColor-themeDark;
}
.ms-bgColor-themeDarkAlt,
.ms-bgColor-themeDarkAlt--hover:hover {
  @include ms-bgColor-themeDarkAlt;
}
.ms-bgColor-themeDarker,
.ms-bgColor-themeDarker--hover:hover {
  @include ms-bgColor-themeDarker;
}
.ms-bgColor-themePrimary,
.ms-bgColor-themePrimary--hover:hover {
  @include ms-bgColor-themePrimary;
}
.ms-bgColor-themeSecondary,
.ms-bgColor-themeSecondary--hover:hover {
  @include ms-bgColor-themeSecondary;
}
.ms-bgColor-themeTertiary,
.ms-bgColor-themeTertiary--hover:hover {
  @include ms-bgColor-themeTertiary;
}
.ms-bgColor-themeLight,
.ms-bgColor-themeLight--hover:hover {
  @include ms-bgColor-themeLight;
}
.ms-bgColor-themeLighter,
.ms-bgColor-themeLighter--hover:hover {
  @include ms-bgColor-themeLighter;
}
.ms-bgColor-themeLighterAlt,
.ms-bgColor-themeLighterAlt--hover:hover {
  @include ms-bgColor-themeLighterAlt;
}

// Border
.ms-borderColor-themeDark,
.ms-borderColor-themeDark--hover:hover {
  @include ms-borderColor-themeDark;
}
.ms-borderColor-themeDarkAlt,
.ms-borderColor-themeDarkAlt--hover:hover {
  @include ms-borderColor-themeDarkAlt;
}
.ms-borderColor-themeDarker,
.ms-borderColor-themeDarker--hover:hover {
  @include ms-borderColor-themeDarker;
}
.ms-borderColor-themePrimary,
.ms-borderColor-themePrimary--hover:hover {
  @include ms-borderColor-themePrimary;
}
.ms-borderColor-themeSecondary,
.ms-borderColor-themeSecondary--hover:hover {
  @include ms-borderColor-themeSecondary;
}
.ms-borderColor-themeTertiary,
.ms-borderColor-themeTertiary--hover:hover {
  @include ms-borderColor-themeTertiary;
}
.ms-borderColor-themeLight,
.ms-borderColor-themeLight--hover:hover {
  @include ms-borderColor-themeLight;
}
.ms-borderColor-themeLighter,
.ms-borderColor-themeLighter--hover:hover {
  @include ms-borderColor-themeLighter;
}
.ms-borderColor-themeLighterAlt,
.ms-borderColor-themeLighterAlt--hover:hover {
  @include ms-borderColor-themeLighterAlt;
}

// Font
.ms-fontColor-themeDarker,
.ms-fontColor-themeDarker--hover:hover {
  @include ms-fontColor-themeDarker;
}
.ms-fontColor-themeDark,
.ms-fontColor-themeDark--hover:hover {
  @include ms-fontColor-themeDark;
}
.ms-fontColor-themeDarkAlt,
.ms-fontColor-themeDarkAlt--hover:hover {
  @include ms-fontColor-themeDarkAlt;
}
.ms-fontColor-themePrimary,
.ms-fontColor-themePrimary--hover:hover {
  @include ms-fontColor-themePrimary;
}
.ms-fontColor-themeSecondary,
.ms-fontColor-themeSecondary--hover:hover {
  @include ms-fontColor-themeSecondary;
}
.ms-fontColor-themeTertiary,
.ms-fontColor-themeTertiary--hover:hover {
  @include ms-fontColor-themeTertiary;
}
.ms-fontColor-themeLight,
.ms-fontColor-themeLight--hover:hover {
  @include ms-fontColor-themeLight;
}
.ms-fontColor-themeLighter,
.ms-fontColor-themeLighter--hover:hover {
  @include ms-fontColor-themeLighter;
}
.ms-fontColor-themeLighterAlt,
.ms-fontColor-themeLighterAlt--hover:hover {
  @include ms-fontColor-themeLighterAlt;
}

//== Neutral colors
//

// Background
.ms-bgColor-black,
.ms-bgColor-black--hover:hover {
  @include ms-bgColor-black;
}
.ms-bgColor-neutralDark,
.ms-bgColor-neutralDark--hover:hover {
  @include ms-bgColor-neutralDark;
}
.ms-bgColor-neutralPrimary,
.ms-bgColor-neutralPrimary--hover:hover {
  @include ms-bgColor-neutralPrimary;
}
.ms-bgColor-neutralPrimaryAlt,
.ms-bgColor-neutralPrimaryAlt--hover:hover {
  @include ms-bgColor-neutralPrimaryAlt;
}
.ms-bgColor-neutralSecondary,
.ms-bgColor-neutralSecondary--hover:hover {
  @include ms-bgColor-neutralSecondary;
}
.ms-bgColor-neutralSecondaryAlt,
.ms-bgColor-neutralSecondaryAlt--hover:hover {
  @include ms-bgColor-neutralSecondaryAlt;
}
.ms-bgColor-neutralTertiary,
.ms-bgColor-neutralTertiary--hover:hover {
  @include ms-bgColor-neutralTertiary;
}
.ms-bgColor-neutralTertiaryAlt,
.ms-bgColor-neutralTertiaryAlt--hover:hover {
  @include ms-bgColor-neutralTertiaryAlt;
}
.ms-bgColor-neutralQuaternary,
.ms-bgColor-neutralQuaternary--hover:hover {
  @include ms-bgColor-neutralQuaternary;
}
.ms-bgColor-neutralQuaternaryAlt,
.ms-bgColor-neutralQuaternaryAlt--hover:hover {
  @include ms-bgColor-neutralQuaternaryAlt;
}
.ms-bgColor-neutralLight,
.ms-bgColor-neutralLight--hover:hover {
  @include ms-bgColor-neutralLight;
}
.ms-bgColor-neutralLighter,
.ms-bgColor-neutralLighter--hover:hover {
  @include ms-bgColor-neutralLighter;
}
.ms-bgColor-neutralLighterAlt,
.ms-bgColor-neutralLighterAlt--hover:hover {
  @include ms-bgColor-neutralLighterAlt;
}
.ms-bgColor-white,
.ms-bgColor-white--hover:hover {
  @include ms-bgColor-white;
}

// Border
.ms-borderColor-black,
.ms-borderColor-black--hover:hover {
  @include ms-borderColor-black;
}
.ms-borderColor-neutralDark,
.ms-borderColor-neutralDark--hover:hover {
  @include ms-borderColor-neutralDark;
}
.ms-borderColor-neutralPrimary,
.ms-borderColor-neutralPrimary--hover:hover {
  @include ms-borderColor-neutralPrimary;
}
.ms-borderColor-neutralPrimaryAlt,
.ms-borderColor-neutralPrimaryAlt--hover:hover {
  @include ms-borderColor-neutralPrimaryAlt;
}
.ms-borderColor-neutralSecondary,
.ms-borderColor-neutralSecondary--hover:hover {
  @include ms-borderColor-neutralSecondary;
}
.ms-borderColor-neutralSecondaryAlt,
.ms-borderColor-neutralSecondaryAlt--hover:hover {
  @include ms-borderColor-neutralSecondaryAlt;
}
.ms-borderColor-neutralTertiary,
.ms-borderColor-neutralTertiary--hover:hover {
  @include ms-borderColor-neutralTertiary;
}
.ms-borderColor-neutralTertiaryAlt,
.ms-borderColor-neutralTertiaryAlt--hover:hover {
  @include ms-borderColor-neutralTertiaryAlt;
}
.ms-borderColor-neutralQuaternary,
.ms-borderColor-neutralQuaternary--hover:hover {
  @include ms-borderColor-neutralQuaternary;
}
.ms-borderColor-neutralQuaternaryAlt,
.ms-borderColor-neutralQuaternaryAlt--hover:hover {
  @include ms-borderColor-neutralQuaternaryAlt;
}
.ms-borderColor-neutralLight,
.ms-borderColor-neutralLight--hover:hover {
  @include ms-borderColor-neutralLight;
}
.ms-borderColor-neutralLighter,
.ms-borderColor-neutralLighter--hover:hover {
  @include ms-borderColor-neutralLighter;
}
.ms-borderColor-neutralLighterAlt,
.ms-borderColor-neutralLighterAlt--hover:hover {
  @include ms-borderColor-neutralLighterAlt;
}
.ms-borderColor-white,
.ms-borderColor-white--hover:hover {
  @include ms-borderColor-white;
}

// Font
.ms-fontColor-black,
.ms-fontColor-black--hover:hover {
  @include ms-fontColor-black;
}
.ms-fontColor-neutralDark,
.ms-fontColor-neutralDark--hover:hover {
  @include ms-fontColor-neutralDark;
}
.ms-fontColor-neutralPrimary,
.ms-fontColor-neutralPrimary--hover:hover {
  @include ms-fontColor-neutralPrimary;
}
.ms-fontColor-neutralPrimaryAlt,
.ms-fontColor-neutralPrimaryAlt--hover:hover {
  @include ms-fontColor-neutralPrimaryAlt;
}
.ms-fontColor-neutralSecondary,
.ms-fontColor-neutralSecondary--hover:hover {
  @include ms-fontColor-neutralSecondary;
}
.ms-fontColor-neutralSecondaryAlt,
.ms-fontColor-neutralSecondaryAlt--hover:hover {
  @include ms-fontColor-neutralSecondaryAlt;
}
.ms-fontColor-neutralTertiary,
.ms-fontColor-neutralTertiary--hover:hover {
  @include ms-fontColor-neutralTertiary;
}
.ms-fontColor-neutralTertiaryAlt,
.ms-fontColor-neutralTertiaryAlt--hover:hover {
  @include ms-fontColor-neutralTertiaryAlt;
}
.ms-fontColor-neutralQuaternary,
.ms-fontColor-neutralQuaternary--hover:hover {
  @include ms-fontColor-neutralQuaternary;
}
.ms-fontColor-neutralQuaternaryAlt,
.ms-fontColor-neutralQuaternaryAlt--hover:hover {
  @include ms-fontColor-neutralQuaternaryAlt;
}
.ms-fontColor-neutralLight,
.ms-fontColor-neutralLight--hover:hover {
  @include ms-fontColor-neutralLight;
}
.ms-fontColor-neutralLighter,
.ms-fontColor-neutralLighter--hover:hover {
  @include ms-fontColor-neutralLighter;
}
.ms-fontColor-neutralLighterAlt,
.ms-fontColor-neutralLighterAlt--hover:hover {
  @include ms-fontColor-neutralLighterAlt;
}
.ms-fontColor-white,
.ms-fontColor-white--hover:hover {
  @include ms-fontColor-white;
}

//== Brand and accent colors
//

// Background
.ms-bgColor-yellow,
.ms-bgColor-yellow--hover:hover {
  @include ms-bgColor-yellow;
}
.ms-bgColor-yellowLight,
.ms-bgColor-yellowLight--hover:hover {
  @include ms-bgColor-yellowLight;
}
.ms-bgColor-orange,
.ms-bgColor-orange--hover:hover {
  @include ms-bgColor-orange;
}
.ms-bgColor-orangeLight,
.ms-bgColor-orangeLight--hover:hover {
  @include ms-bgColor-orangeLight;
}
.ms-bgColor-orangeLighter,
.ms-bgColor-orangeLighter--hover:hover {
  @include ms-bgColor-orangeLighter;
}
.ms-bgColor-redDark,
.ms-bgColor-redDark--hover:hover {
  @include ms-bgColor-redDark;
}
.ms-bgColor-red,
.ms-bgColor-red--hover:hover {
  @include ms-bgColor-red;
}
.ms-bgColor-magentaDark,
.ms-bgColor-magentaDark--hover:hover {
  @include ms-bgColor-magentaDark;
}
.ms-bgColor-magenta,
.ms-bgColor-magenta--hover:hover {
  @include ms-bgColor-magenta;
}
.ms-bgColor-magentaLight,
.ms-bgColor-magentaLight--hover:hover {
  @include ms-bgColor-magentaLight;
}
.ms-bgColor-purpleDark,
.ms-bgColor-purpleDark--hover:hover {
  @include ms-bgColor-purpleDark;
}
.ms-bgColor-purple,
.ms-bgColor-purple--hover:hover {
  @include ms-bgColor-purple;
}
.ms-bgColor-purpleLight,
.ms-bgColor-purpleLight--hover:hover {
  @include ms-bgColor-purpleLight;
}
.ms-bgColor-blueDark,
.ms-bgColor-blueDark--hover:hover {
  @include ms-bgColor-blueDark;
}
.ms-bgColor-blueMid,
.ms-bgColor-blueMid--hover:hover {
  @include ms-bgColor-blueMid;
}
.ms-bgColor-blue,
.ms-bgColor-blue--hover:hover {
  @include ms-bgColor-blue;
}
.ms-bgColor-blueLight,
.ms-bgColor-blueLight--hover:hover {
  @include ms-bgColor-blueLight;
}
.ms-bgColor-tealDark,
.ms-bgColor-tealDark--hover:hover {
  @include ms-bgColor-tealDark;
}
.ms-bgColor-teal,
.ms-bgColor-teal--hover:hover {
  @include ms-bgColor-teal;
}
.ms-bgColor-tealLight,
.ms-bgColor-tealLight--hover:hover {
  @include ms-bgColor-tealLight;
}
.ms-bgColor-greenDark,
.ms-bgColor-greenDark--hover:hover {
  @include ms-bgColor-greenDark;
}
.ms-bgColor-green,
.ms-bgColor-green--hover:hover {
  @include ms-bgColor-green;
}
.ms-bgColor-greenLight,
.ms-bgColor-greenLight--hover:hover {
  @include ms-bgColor-greenLight;
}

// Border
.ms-borderColor-yellow,
.ms-borderColor-yellow--hover:hover {
  @include ms-borderColor-yellow;
}
.ms-borderColor-yellowLight,
.ms-borderColor-yellowLight--hover:hover {
  @include ms-borderColor-yellowLight;
}
.ms-borderColor-orange,
.ms-borderColor-orange--hover:hover {
  @include ms-borderColor-orange;
}
.ms-borderColor-orangeLight,
.ms-borderColor-orangeLight--hover:hover {
  @include ms-borderColor-orangeLight;
}
.ms-borderColor-orangeLighter,
.ms-borderColor-orangeLighter--hover:hover {
  @include ms-borderColor-orangeLighter;
}
.ms-borderColor-redDark,
.ms-borderColor-redDark--hover:hover {
  @include ms-borderColor-redDark;
}
.ms-borderColor-red,
.ms-borderColor-red--hover:hover {
  @include ms-borderColor-red;
}
.ms-borderColor-magentaDark,
.ms-borderColor-magentaDark--hover:hover {
  @include ms-borderColor-magentaDark;
}
.ms-borderColor-magenta,
.ms-borderColor-magenta--hover:hover {
  @include ms-borderColor-magenta;
}
.ms-borderColor-magentaLight,
.ms-borderColor-magentaLight--hover:hover {
  @include ms-borderColor-magentaLight;
}
.ms-borderColor-purpleDark,
.ms-borderColor-purpleDark--hover:hover {
  @include ms-borderColor-purpleDark;
}
.ms-borderColor-purple,
.ms-borderColor-purple--hover:hover {
  @include ms-borderColor-purple;
}
.ms-borderColor-purpleLight,
.ms-borderColor-purpleLight--hover:hover {
  @include ms-borderColor-purpleLight;
}
.ms-borderColor-blueDark,
.ms-borderColor-blueDark--hover:hover {
  @include ms-borderColor-blueDark;
}
.ms-borderColor-blueMid,
.ms-borderColor-blueMid--hover:hover {
  @include ms-borderColor-blueMid;
}
.ms-borderColor-blue,
.ms-borderColor-blue--hover:hover {
  @include ms-borderColor-blue;
}
.ms-borderColor-blueLight,
.ms-borderColor-blueLight--hover:hover {
  @include ms-borderColor-blueLight;
}
.ms-borderColor-tealDark,
.ms-borderColor-tealDark--hover:hover {
  @include ms-borderColor-tealDark;
}
.ms-borderColor-teal,
.ms-borderColor-teal--hover:hover {
  @include ms-borderColor-teal;
}
.ms-borderColor-tealLight,
.ms-borderColor-tealLight--hover:hover {
  @include ms-borderColor-tealLight;
}
.ms-borderColor-greenDark,
.ms-borderColor-greenDark--hover:hover {
  @include ms-borderColor-greenDark;
}
.ms-borderColor-green,
.ms-borderColor-green--hover:hover {
  @include ms-borderColor-green;
}
.ms-borderColor-greenLight,
.ms-borderColor-greenLight--hover:hover {
  @include ms-borderColor-greenLight;
}

// Font
.ms-fontColor-yellow,
.ms-fontColor-yellow--hover:hover {
  @include ms-fontColor-yellow;
}
.ms-fontColor-yellowLight,
.ms-fontColor-yellowLight--hover:hover {
  @include ms-fontColor-yellowLight;
}
.ms-fontColor-orange,
.ms-fontColor-orange--hover:hover {
  @include ms-fontColor-orange;
}
.ms-fontColor-orangeLight,
.ms-fontColor-orangeLight--hover:hover {
  @include ms-fontColor-orangeLight;
}
.ms-fontColor-orangeLighter,
.ms-fontColor-orangeLighter--hover:hover {
  @include ms-fontColor-orangeLighter;
}
.ms-fontColor-redDark,
.ms-fontColor-redDark--hover:hover {
  @include ms-fontColor-redDark;
}
.ms-fontColor-red,
.ms-fontColor-red--hover:hover {
  @include ms-fontColor-red;
}
.ms-fontColor-magentaDark,
.ms-fontColor-magentaDark--hover:hover {
  @include ms-fontColor-magentaDark;
}
.ms-fontColor-magenta,
.ms-fontColor-magenta--hover:hover {
  @include ms-fontColor-magenta;
}
.ms-fontColor-magentaLight,
.ms-fontColor-magentaLight--hover:hover {
  @include ms-fontColor-magentaLight;
}
.ms-fontColor-purpleDark,
.ms-fontColor-purpleDark--hover:hover {
  @include ms-fontColor-purpleDark;
}
.ms-fontColor-purple,
.ms-fontColor-purple--hover:hover {
  @include ms-fontColor-purple;
}
.ms-fontColor-purpleLight,
.ms-fontColor-purpleLight--hover:hover {
  @include ms-fontColor-purpleLight;
}
.ms-fontColor-blueDark,
.ms-fontColor-blueDark--hover:hover {
  @include ms-fontColor-blueDark;
}
.ms-fontColor-blueMid,
.ms-fontColor-blueMid--hover:hover {
  @include ms-fontColor-blueMid;
}
.ms-fontColor-blue,
.ms-fontColor-blue--hover:hover {
  @include ms-fontColor-blue;
}
.ms-fontColor-blueLight,
.ms-fontColor-blueLight--hover:hover {
  @include ms-fontColor-blueLight;
}
.ms-fontColor-tealDark,
.ms-fontColor-tealDark--hover:hover {
  @include ms-fontColor-tealDark;
}
.ms-fontColor-teal,
.ms-fontColor-teal--hover:hover {
  @include ms-fontColor-teal;
}
.ms-fontColor-tealLight,
.ms-fontColor-tealLight--hover:hover {
  @include ms-fontColor-tealLight;
}
.ms-fontColor-greenDark,
.ms-fontColor-greenDark--hover:hover {
  @include ms-fontColor-greenDark;
}
.ms-fontColor-green,
.ms-fontColor-green--hover:hover {
  @include ms-fontColor-green;
}
.ms-fontColor-greenLight,
.ms-fontColor-greenLight--hover:hover {
  @include ms-fontColor-greenLight;
}

//== Message colors
//

// Background
.ms-bgColor-info,
.ms-bgColor-info--hover:hover {
  @include ms-bgColor-info;
}
.ms-bgColor-success,
.ms-bgColor-success--hover:hover {
  @include ms-bgColor-success;
}
.ms-bgColor-severeWarning,
.ms-bgColor-severeWarning--hover:hover {
  @include ms-bgColor-severeWarning;
}
.ms-bgColor-warning,
.ms-bgColor-warning--hover:hover {
  @include ms-bgColor-warning;
}
.ms-bgColor-error,
.ms-bgColor-error--hover:hover {
  @include ms-bgColor-error;
}

// Border
// Classes were never provided for message color borders.

// Font
.ms-fontColor-info,
.ms-fontColor-info--hover:hover {
  @include ms-fontColor-info;
}
.ms-fontColor-success,
.ms-fontColor-success--hover:hover {
  @include ms-fontColor-success;
}
.ms-fontColor-alert,
.ms-fontColor-alert--hover:hover {
  // Deprecated: Use ms-fontColor-severeWarning
  @include ms-fontColor-alert;
}
.ms-fontColor-warning,
.ms-fontColor-warning--hover:hover {
  @include ms-fontColor-warning;
}
.ms-fontColor-severeWarning,
.ms-fontColor-severeWarning--hover:hover {
  @include ms-fontColor-severeWarning;
}
.ms-fontColor-error,
.ms-fontColor-error--hover:hover {
  @include ms-fontColor-error;
}

//== High contrast colors
//

// Background
.ms-bgColor-contrastBlackDisabled,
.ms-bgColor-contrastBlackDisabled--hover:hover {
  @include ms-bgColor-contrastBlackDisabled;
}
.ms-bgColor-contrastWhiteDisabled,
.ms-bgColor-contrastWhiteDisabled--hover:hover {
  @include ms-bgColor-contrastWhiteDisabled;
}
.ms-bgColor-contrastBlackSelected,
.ms-bgColor-contrastBlackSelected--hover:hover {
  @include ms-bgColor-contrastBlackSelected;
}
.ms-bgColor-contrastWhiteSelected,
.ms-bgColor-contrastWhiteSelected--hover:hover {
  @include ms-bgColor-contrastWhiteSelected;
}

// Font
.ms-fontColor-contrastBlackDisabled,
.ms-fontColor-contrastBlackDisabled--hover:hover {
  @include ms-fontColor-contrastBlackDisabled;
}
.ms-fontColor-contrastWhiteDisabled,
.ms-fontColor-contrastWhiteDisabled--hover:hover {
  @include ms-fontColor-contrastWhiteDisabled;
}
.ms-fontColor-contrastBlackSelected,
.ms-fontColor-contrastBlackSelected--hover:hover {
  @include ms-fontColor-contrastBlackSelected;
}
.ms-fontColor-contrastWhiteSelected,
.ms-fontColor-contrastWhiteSelected--hover:hover {
  @include ms-fontColor-contrastWhiteSelected;
}

// Border
.ms-borderColor-contrastBlackDisabled,
.ms-borderColor-contrastBlackDisabled--hover:hover {
  @include ms-borderColor-contrastBlackDisabled;
}
.ms-borderColor-contrastWhiteDisabled,
.ms-borderColor-contrastWhiteDisabled--hover:hover {
  @include ms-borderColor-contrastWhiteDisabled;
}
.ms-borderColor-contrastBlackSelected,
.ms-borderColor-contrastBlackSelected--hover:hover {
  @include ms-borderColor-contrastBlackSelected;
}
.ms-borderColor-contrastWhiteSelected,
.ms-borderColor-contrastWhiteSelected--hover:hover {
  @include ms-borderColor-contrastWhiteSelected;
}

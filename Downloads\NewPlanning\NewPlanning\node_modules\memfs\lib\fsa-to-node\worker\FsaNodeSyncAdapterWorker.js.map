{"version": 3, "file": "FsaNodeSyncAdapterWorker.js", "sourceRoot": "", "sources": ["../../../src/fsa-to-node/worker/FsaNodeSyncAdapterWorker.ts"], "names": [], "mappings": ";;;AAAA,gDAA6C;AAE7C,mDAAgD;AAChD,kCAA2C;AAa3C,IAAI,MAAM,GAAG,CAAC,CAAC;AAEf,MAAa,wBAAwB;IAC5B,MAAM,CAAC,KAAK,CAAC,KAAK,CACvB,GAAW,EACX,GAA6E;QAE7E,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;QAC/B,MAAM,MAAM,GAAG,IAAI,aAAK,EAA4B,CAAC;QACrD,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC;QAClB,IAAI,SAAS,GAA8B,SAAS,CAAC;QACrD,MAAM,IAAI,GAAG,MAAM,GAAG,CAAC;QACvB,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE;YACrB,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;YACpB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC;gBAAE,OAAO;YACjC,MAAM,GAAG,GAAG,IAAwB,CAAC;YACrC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAA6B,CAAC;YAChD,QAAQ,IAAI,EAAE,CAAC;gBACb,0CAAkC,CAAC,CAAC,CAAC;oBACnC,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,GAA2B,CAAC;oBAC5C,SAAS,GAAG,IAAI,6BAAa,CAAC,GAAG,CAAC,CAAC;oBACnC,MAAM,cAAc,GAA4B,2CAAmC,EAAE,EAAE,IAAI,CAAC,CAAC;oBAC7F,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;oBACnC,MAAM;gBACR,CAAC;gBACD,6CAAqC,CAAC,CAAC,CAAC;oBACtC,MAAM,CAAC,EAAE,MAAM,CAAC,GAAG,GAA8B,CAAC;oBAClD,IAAI,EAAE,KAAK,MAAM;wBAAE,OAAO;oBAC1B,MAAM,OAAO,GAAG,IAAI,wBAAwB,CAAC,SAAU,EAAE,IAAI,CAAC,CAAC;oBAC/D,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACxB,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC,CAAC;QACF,OAAO,MAAM,MAAM,CAAC,OAAO,CAAC;IAC9B,CAAC;IAED,YACqB,SAAwB,EACxB,IAAoC;QADpC,cAAS,GAAT,SAAS,CAAe;QACxB,SAAI,GAAJ,IAAI,CAAgC;IACtD,CAAC;IAEG,IAAI,CACT,MAAS,EACT,OAAgD;QAEhD,MAAM,OAAO,GAA4B,2CAAmC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7F,MAAM,OAAO,GAAG,cAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAEzD,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,cAAO,CAAC,MAAM,CAAC,eAAe,CAAY,CAAC;QAChE,QAAQ,IAAI,EAAE,CAAC;YACb;gBACE,OAAO,IAAW,CAAC;YACrB;gBACE,MAAM,IAAI,CAAC;YACb,OAAO,CAAC,CAAC,CAAC;gBACR,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;YACnD,CAAC;QACH,CAAC;IACH,CAAC;CACF;AA3DD,4DA2DC"}
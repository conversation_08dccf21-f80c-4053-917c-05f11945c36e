/*!
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
import { DummyInputObserver as DummyInputObserverInterface, GetWindow, RadioButtonGroup, SysProps, TabsterAttributeProps, TabsterCore, TabsterPart as Tabster<PERSON>artInterface, Visibility, WeakHTMLElement as WeakHTMLElementInterface } from "./Types";
interface HTMLElementWithBoundingRectCacheId extends HTMLElement {
    __tabsterCacheId?: string;
}
export interface WindowWithUID extends Window {
    __tabsterCrossOriginWindowUID?: string;
}
export interface HTMLElementWithUID extends HTMLElement {
    __tabsterElementUID?: string;
}
export interface TabsterDOMRect {
    bottom: number;
    left: number;
    right: number;
    top: number;
}
export interface InstanceContext {
    elementByUId: {
        [uid: string]: WeakHTMLElement<HTMLElementWithUID>;
    };
    basics: InternalBasics;
    WeakRef?: WeakRefConstructor;
    containerBoundingRectCache: {
        [id: string]: {
            rect: TabsterDOMRect;
            element: HTMLElementWithBoundingRectCacheId;
        };
    };
    lastContainerBoundingRectCacheId: number;
    containerBoundingRectCacheTimer?: number;
    fakeWeakRefs: TabsterWeakRef<unknown>[];
    fakeWeakRefsTimer?: number;
    fakeWeakRefsStarted: boolean;
}
export declare function getInstanceContext(getWindow: GetWindow): InstanceContext;
export declare function disposeInstanceContext(win: Window): void;
export declare function createWeakMap<K extends object, V>(win: Window): WeakMap<K, V>;
export declare function hasSubFocusable(element: HTMLElement): boolean;
interface TabsterWeakRef<T> {
    deref(): T | undefined;
}
export declare class WeakHTMLElement<T extends HTMLElement = HTMLElement, D = undefined> implements WeakHTMLElementInterface<D> {
    private _ref;
    private _data;
    constructor(getWindow: GetWindow, element: T, data?: D);
    get(): T | undefined;
    getData(): D | undefined;
}
export declare function cleanupFakeWeakRefs(getWindow: GetWindow, forceRemove?: boolean): void;
export declare function startFakeWeakRefsCleanup(getWindow: GetWindow): void;
export declare function stopFakeWeakRefsCleanupAndClearStorage(getWindow: GetWindow): void;
export declare function createElementTreeWalker(doc: Document, root: Node, acceptNode: (node: Node) => number): TreeWalker | undefined;
export declare function getBoundingRect(getWindow: GetWindow, element: HTMLElementWithBoundingRectCacheId): TabsterDOMRect;
export declare function isElementVerticallyVisibleInContainer(getWindow: GetWindow, element: HTMLElement, tolerance: number): boolean;
export declare function isElementVisibleInContainer(getWindow: GetWindow, element: HTMLElement, gap?: number): Visibility;
export declare function scrollIntoView(getWindow: GetWindow, element: HTMLElement, alignToTop: boolean): void;
export declare function getScrollableContainer(element: HTMLElement): HTMLElement | null;
export declare function makeFocusIgnored(element: HTMLElement): void;
export declare function shouldIgnoreFocus(element: HTMLElement): boolean;
export declare function getUId(wnd: Window & {
    msCrypto?: Crypto;
}): string;
export declare function getElementUId(getWindow: GetWindow, element: HTMLElementWithUID): string;
export declare function getElementByUId(context: InstanceContext, uid: string): WeakHTMLElement<HTMLElementWithUID, undefined> | undefined;
export declare function getWindowUId(win: WindowWithUID): string;
export declare function clearElementCache(getWindow: GetWindow, parent?: HTMLElement): void;
export declare function documentContains(doc: HTMLDocument | null | undefined, element: HTMLElement): boolean;
export declare function matchesSelector(element: HTMLElement, selector: string): boolean;
export declare function getPromise(getWindow: GetWindow): PromiseConstructor;
export declare function getWeakRef(context: InstanceContext): WeakRefConstructor | undefined;
interface InternalBasics {
    Promise?: PromiseConstructor;
    WeakRef?: WeakRefConstructor;
    WeakMap?: WeakMapConstructor;
}
export declare function setBasics(win: Window, basics: InternalBasics): void;
export declare abstract class TabsterPart<P, D = undefined> implements TabsterPartInterface<P> {
    protected _tabster: TabsterCore;
    protected _element: WeakHTMLElement<HTMLElement, D>;
    protected _props: P;
    readonly id: string;
    constructor(tabster: TabsterCore, element: HTMLElement, props: P);
    getElement(): HTMLElement | undefined;
    getProps(): P;
    setProps(props: P): void;
}
export interface DummyInputProps {
    /** The input is created to be used only once and autoremoved when focused. */
    isPhantom?: boolean;
    /** Whether the input is before or after the content it is guarding.  */
    isFirst: boolean;
}
export type DummyInputFocusCallback = (dummyInput: DummyInput, isBackward: boolean, relatedTarget: HTMLElement | null) => void;
/**
 * Dummy HTML elements that are used as focus sentinels for the DOM enclosed within them
 */
export declare class DummyInput {
    private _isPhantom;
    private _fixedTarget?;
    private _disposeTimer;
    private _clearDisposeTimeout;
    input: HTMLElement | undefined;
    useDefaultAction?: boolean;
    isFirst: DummyInputProps["isFirst"];
    isOutside: boolean;
    /** Called when the input is focused */
    onFocusIn?: DummyInputFocusCallback;
    /** Called when the input is blurred */
    onFocusOut?: DummyInputFocusCallback;
    constructor(getWindow: GetWindow, isOutside: boolean, props: DummyInputProps, element?: WeakHTMLElement, fixedTarget?: WeakHTMLElement);
    dispose(): void;
    setTopLeft(top: number, left: number): void;
    private _isBackward;
    private _focusIn;
    private _focusOut;
}
export declare const DummyInputManagerPriorities: {
    readonly Root: 1;
    readonly Modalizer: 2;
    readonly Mover: 3;
    readonly Groupper: 4;
};
export declare class DummyInputManager {
    private _instance?;
    private _onFocusIn?;
    private _onFocusOut?;
    protected _element: WeakHTMLElement;
    constructor(tabster: TabsterCore, element: WeakHTMLElement, priority: number, sys: SysProps | undefined, outsideByDefault?: boolean, callForDefaultAction?: boolean);
    protected _setHandlers(onFocusIn?: DummyInputFocusCallback, onFocusOut?: DummyInputFocusCallback): void;
    moveOut(backwards: boolean): void;
    moveOutWithDefaultAction(backwards: boolean, relatedEvent: KeyboardEvent): void;
    getHandler(isIn: boolean): DummyInputFocusCallback | undefined;
    setTabbable(tabbable: boolean): void;
    dispose(): void;
    static moveWithPhantomDummy(tabster: TabsterCore, element: HTMLElement, // The target element to move to or out of.
    moveOutOfElement: boolean, // Whether to move out of the element or into it.
    isBackward: boolean, // Are we tabbing of shift-tabbing?
    relatedEvent: KeyboardEvent): void;
    static addPhantomDummyWithTarget(tabster: TabsterCore, sourceElement: HTMLElement, isBackward: boolean, targetElement: HTMLElement): void;
}
export declare class DummyInputObserver implements DummyInputObserverInterface {
    private _win?;
    private _updateQueue;
    private _updateTimer?;
    private _lastUpdateQueueTime;
    private _changedParents;
    private _updateDummyInputsTimer?;
    private _dummyElements;
    private _dummyCallbacks;
    domChanged?(parent: HTMLElement): void;
    constructor(win: GetWindow);
    add(dummy: HTMLElement, callback: () => void): void;
    remove(dummy: HTMLElement): void;
    dispose(): void;
    private _domChanged;
    updatePositions(compute: (scrollTopLeftCache: Map<HTMLElement, {
        scrollTop: number;
        scrollLeft: number;
    } | null>) => () => void): void;
    private _scheduledUpdatePositions;
}
export declare function getLastChild(container: HTMLElement): HTMLElement | undefined;
export declare function getAdjacentElement(from: HTMLElement, prev?: boolean): HTMLElement | undefined;
export declare function augmentAttribute(tabster: TabsterCore, element: HTMLElement, name: string, value?: string | null): boolean;
export declare function getTabsterAttributeOnElement(element: HTMLElement): TabsterAttributeProps | null;
export declare function isDisplayNone(element: HTMLElement): boolean;
export declare function isRadio(element: HTMLElement): boolean;
export declare function getRadioButtonGroup(element: HTMLElement): RadioButtonGroup | undefined;
/**
 * If the passed element is Tabster dummy input, returns the container element this dummy input belongs to.
 * @param element Element to check for being dummy input.
 * @returns Dummy input container element (if the passed element is a dummy input) or null.
 */
export declare function getDummyInputContainer(element: HTMLElement | null | undefined): HTMLElement | null;
export {};

{"version": 3, "file": "LargeFileUploadTask.js", "sourceRoot": "", "sources": ["../../../src/tasks/LargeFileUploadTask.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAOH,kCAAiC;AAuDjC;;;GAGG;AACH;IA+DC;;;;;;;;;OASG;IACH,6BAAmB,MAAc,EAAE,IAAgB,EAAE,aAAqC,EAAE,OAAwC;QAAxC,wBAAA,EAAA,YAAwC;QAxEpI;;;WAGG;QACK,sBAAiB,GAAW,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;QAqEnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,OAAO,CAAC,SAAS,KAAK,SAAS,EAAE;YACpC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC;SAC3C;QACD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,aAAK,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;IAC3D,CAAC;IA7CD;;;;;;;;;OASG;IACiB,uCAAmB,GAAvC,UAAwC,MAAc,EAAE,UAAkB,EAAE,OAAY,EAAE,OAA4C;QAA5C,wBAAA,EAAA,YAA4C;;;;;;;wBAEpH,qBAAM,MAAM;iCAC1B,GAAG,CAAC,UAAU,CAAC;iCACf,OAAO,CAAC,OAAO,CAAC;iCAChB,IAAI,CAAC,OAAO,CAAC,EAAA;;wBAHT,OAAO,GAAG,SAGD;wBACT,sBAAsB,GAA2B;4BACtD,GAAG,EAAE,OAAO,CAAC,SAAS;4BACtB,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC;yBAC5C,CAAC;wBACF,sBAAO,sBAAsB,EAAC;;;wBAE9B,MAAM,KAAG,CAAC;;;;;KAEX;IAuBD;;;;;OAKG;IACK,wCAAU,GAAlB,UAAmB,MAAgB;QAClC,IAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;QAC3B,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,QAAQ,KAAK,EAAE,EAAE;YACvD,OAAO,IAAI,aAAK,EAAE,CAAC;SACnB;QACD,IAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACvC,IAAM,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAC3C,IAAI,MAAM,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACzC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SAC5B;QACD,OAAO,IAAI,aAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAClC,CAAC;IAED;;;;;OAKG;IACK,8CAAgB,GAAxB,UAAyB,QAA8B;QACtD,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QAClE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IAC/D,CAAC;IAED;;;;OAIG;IACI,0CAAY,GAAnB;QACC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC,SAAS,CAAC;SACtB;QACD,IAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACvC,IAAI,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;QACnD,IAAI,QAAQ,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAC/B,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;SAC9B;QACD,OAAO,IAAI,aAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;;;;OAKG;IACI,uCAAS,GAAhB,UAAiB,KAAY;QAC5B,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IACb,CAAC;IAED;;;;;OAKG;IACU,oCAAM,GAAnB;;;;;;;;;6BAES,IAAI;wBACJ,SAAS,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;wBACtC,IAAI,SAAS,CAAC,QAAQ,KAAK,CAAC,CAAC,EAAE;4BACxB,GAAG,GAAG,IAAI,KAAK,CAAC,oGAAoG,CAAC,CAAC;4BAC5H,GAAG,CAAC,IAAI,GAAG,iBAAiB,CAAC;4BAC7B,MAAM,GAAG,CAAC;yBACV;wBACK,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;wBAC3B,qBAAM,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAA;;wBAAvE,QAAQ,GAAG,SAA4D;wBAC7E,iGAAiG;wBACjG,IAAI,QAAQ,CAAC,EAAE,KAAK,SAAS,EAAE;4BAC9B,sBAAO,QAAQ,EAAC;yBAChB;6BAAM;4BACN,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;yBAChC;;;;;wBAGF,MAAM,KAAG,CAAC;;;;;KAEX;IAED;;;;;;;OAOG;IACU,yCAAW,GAAxB,UAAyB,SAAoC,EAAE,KAAY,EAAE,SAAiB;;;;;;;wBAErF,qBAAM,IAAI,CAAC,MAAM;iCACtB,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;iCAC3B,OAAO,CAAC;gCACR,gBAAgB,EAAE,MAAG,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAE;gCAC1D,eAAe,EAAE,WAAS,KAAK,CAAC,QAAQ,SAAI,KAAK,CAAC,QAAQ,SAAI,SAAW;6BACzE,CAAC;iCACD,GAAG,CAAC,SAAS,CAAC,EAAA;4BANhB,sBAAO,SAMS,EAAC;;;wBAEjB,MAAM,KAAG,CAAC;;;;;KAEX;IAED;;;;;OAKG;IACU,oCAAM,GAAnB;;;;;;;wBAES,qBAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAA;4BAA7D,sBAAO,SAAsD,EAAC;;;wBAE9D,MAAM,KAAG,CAAC;;;;;KAEX;IAED;;;;;OAKG;IACU,uCAAS,GAAtB;;;;;;;wBAEmB,qBAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAA;;wBAA9D,QAAQ,GAAG,SAAmD;wBACpE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;wBAChC,sBAAO,QAAQ,EAAC;;;wBAEhB,MAAM,KAAG,CAAC;;;;;KAEX;IAED;;;;;OAKG;IACU,oCAAM,GAAnB;;;;;;;wBAEE,qBAAM,IAAI,CAAC,SAAS,EAAE,EAAA;;wBAAtB,SAAsB,CAAC;wBAChB,qBAAM,IAAI,CAAC,MAAM,EAAE,EAAA;4BAA1B,sBAAO,SAAmB,EAAC;;;wBAE3B,MAAM,KAAG,CAAC;;;;;KAEX;IACF,0BAAC;AAAD,CAAC,AA9OD,IA8OC;AA9OY,kDAAmB"}
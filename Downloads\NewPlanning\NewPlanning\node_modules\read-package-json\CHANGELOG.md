# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

<a name="2.1.2"></a>
## [2.1.2](https://github.com/npm/read-package-json/compare/v2.1.1...v2.1.2) (2020-08-20)


### Bug Fixes

* even better json errors, remove graceful-fs ([fdbf082](https://github.com/npm/read-package-json/commit/fdbf082))



<a name="2.1.1"></a>
## [2.1.1](https://github.com/npm/read-package-json/compare/v2.1.0...v2.1.1) (2019-12-09)


### Bug Fixes

* normalize and sanitize pkg bin entries ([b8cb5fa](https://github.com/npm/read-package-json/commit/b8cb5fa))



<a name="2.1.0"></a>
# [2.1.0](https://github.com/npm/read-package-json/compare/v2.0.13...v2.1.0) (2019-08-13)


### Features

* support bundleDependencies: true ([76f6f42](https://github.com/npm/read-package-json/commit/76f6f42))



<a name="2.0.13"></a>
## [2.0.13](https://github.com/npm/read-package-json/compare/v2.0.12...v2.0.13) (2018-03-08)


### Bug Fixes

* **git:** support git packed refs --all mode ([#77](https://github.com/npm/read-package-json/issues/77)) ([1869940](https://github.com/npm/read-package-json/commit/1869940))

// Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license. See L<PERSON>EN<PERSON> in the project root for license information.

// Communication
@mixin ms-borderColor-communicationShade30 {
  border-color: $ms-color-communicationShade30;
}
@mixin ms-borderColor-communicationShade20 {
  border-color: $ms-color-communicationShade20;
}
@mixin ms-borderColor-communicationShade10 {
  border-color: $ms-color-communicationShade10;
}
@mixin ms-borderColor-communicationPrimary {
  border-color: $ms-color-communicationPrimary;
}
@mixin ms-borderColor-communicationTint10 {
  border-color: $ms-color-communicationTint10;
}
@mixin ms-borderColor-communicationTint20 {
  border-color: $ms-color-communicationTint20;
}
@mixin ms-borderColor-communicationTint30 {
  border-color: $ms-color-communicationTint30;
}
@mixin ms-borderColor-communicationTint40 {
  border-color: $ms-color-communicationTint40;
}

// Neutral
@mixin ms-borderColor-black {
  border-color: $ms-color-black;
}
@mixin ms-borderColor-gray220 {
  border-color: $ms-color-gray220;
}
@mixin ms-borderColor-gray210 {
  border-color: $ms-color-gray210;
}
@mixin ms-borderColor-gray200 {
  border-color: $ms-color-gray200;
}
@mixin ms-borderColor-gray190 {
  border-color: $ms-color-gray190;
}
@mixin ms-borderColor-gray180 {
  border-color: $ms-color-gray180;
}
@mixin ms-borderColor-gray170 {
  border-color: $ms-color-gray170;
}
@mixin ms-borderColor-gray160 {
  border-color: $ms-color-gray160;
}
@mixin ms-borderColor-gray150 {
  border-color: $ms-color-gray150;
}
@mixin ms-borderColor-gray140 {
  border-color: $ms-color-gray140;
}
@mixin ms-borderColor-gray130 {
  border-color: $ms-color-gray130;
}
@mixin ms-borderColor-gray120 {
  border-color: $ms-color-gray120;
}
@mixin ms-borderColor-gray110 {
  border-color: $ms-color-gray110;
}
@mixin ms-borderColor-gray100 {
  border-color: $ms-color-gray100;
}
@mixin ms-borderColor-gray90 {
  border-color: $ms-color-gray90;
}
@mixin ms-borderColor-gray80 {
  border-color: $ms-color-gray80;
}
@mixin ms-borderColor-gray70 {
  border-color: $ms-color-gray70;
}
@mixin ms-borderColor-gray60 {
  border-color: $ms-color-gray60;
}
@mixin ms-borderColor-gray50 {
  border-color: $ms-color-gray50;
}
@mixin ms-borderColor-gray40 {
  border-color: $ms-color-gray40;
}
@mixin ms-borderColor-gray30 {
  border-color: $ms-color-gray30;
}
@mixin ms-borderColor-gray20 {
  border-color: $ms-color-gray20;
}
@mixin ms-borderColor-gray10 {
  border-color: $ms-color-gray10;
}
@mixin ms-borderColor-white {
  border-color: $ms-color-white;
}

// Shared
@mixin ms-borderColor-sharedPinkRed10 {
  border-color: $ms-color-sharedPinkRed10;
}
@mixin ms-borderColor-sharedRed20 {
  border-color: $ms-color-sharedRed20;
}
@mixin ms-borderColor-sharedRed10 {
  border-color: $ms-color-sharedRed10;
}
@mixin ms-borderColor-sharedRedOrange20 {
  border-color: $ms-color-sharedRedOrange20;
}
@mixin ms-borderColor-sharedRedOrange10 {
  border-color: $ms-color-sharedRedOrange10;
}
@mixin ms-borderColor-sharedOrange30 {
  border-color: $ms-color-sharedOrange30;
}
@mixin ms-borderColor-sharedOrange20 {
  border-color: $ms-color-sharedOrange20;
}
@mixin ms-borderColor-sharedOrange10 {
  border-color: $ms-color-sharedOrange10;
}
@mixin ms-borderColor-sharedYellow10 {
  border-color: $ms-color-sharedYellow10;
}
@mixin ms-borderColor-sharedOrangeYellow20 {
  border-color: $ms-color-sharedOrangeYellow20;
}
@mixin ms-borderColor-sharedOrangeYellow10 {
  border-color: $ms-color-sharedOrangeYellow10;
}
@mixin ms-borderColor-sharedYellowGreen10 {
  border-color: $ms-color-sharedYellowGreen10;
}
@mixin ms-borderColor-sharedGreen20 {
  border-color: $ms-color-sharedGreen20;
}
@mixin ms-borderColor-sharedGreen10 {
  border-color: $ms-color-sharedGreen10;
}
@mixin ms-borderColor-sharedGreenCyan10 {
  border-color: $ms-color-sharedGreenCyan10;
}
@mixin ms-borderColor-sharedCyan40 {
  border-color: $ms-color-sharedCyan40;
}
@mixin ms-borderColor-sharedCyan30 {
  border-color: $ms-color-sharedCyan30;
}
@mixin ms-borderColor-sharedCyan20 {
  border-color: $ms-color-sharedCyan20;
}
@mixin ms-borderColor-sharedCyan10 {
  border-color: $ms-color-sharedCyan10;
}
@mixin ms-borderColor-sharedCyanBlue20 {
  border-color: $ms-color-sharedCyanBlue20;
}
@mixin ms-borderColor-sharedCyanBlue10 {
  border-color: $ms-color-sharedCyanBlue10;
}
@mixin ms-borderColor-sharedBlue10 {
  border-color: $ms-color-sharedBlue10;
}
@mixin ms-borderColor-sharedBlueMagenta40 {
  border-color: $ms-color-sharedBlueMagenta40;
}
@mixin ms-borderColor-sharedBlueMagenta30 {
  border-color: $ms-color-sharedBlueMagenta30;
}
@mixin ms-borderColor-sharedBlueMagenta20 {
  border-color: $ms-color-sharedBlueMagenta20;
}
@mixin ms-borderColor-sharedBlueMagenta10 {
  border-color: $ms-color-sharedBlueMagenta10;
}
@mixin ms-borderColor-sharedMagenta20 {
  border-color: $ms-color-sharedMagenta20;
}
@mixin ms-borderColor-sharedMagenta10 {
  border-color: $ms-color-sharedMagenta10;
}
@mixin ms-borderColor-sharedMagentaPink20 {
  border-color: $ms-color-sharedMagentaPink20;
}
@mixin ms-borderColor-sharedMagentaPink10 {
  border-color: $ms-color-sharedMagentaPink10;
}
@mixin ms-borderColor-sharedGray40 {
  border-color: $ms-color-sharedGray40;
}
@mixin ms-borderColor-sharedGray30 {
  border-color: $ms-color-sharedGray30;
}
@mixin ms-borderColor-sharedGray20 {
  border-color: $ms-color-sharedGray20;
}
@mixin ms-borderColor-sharedGray10 {
  border-color: $ms-color-sharedGray10;
}
